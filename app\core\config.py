"""
应用配置管理
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置类"""
    
    # DeepSeek API配置
    DEEPSEEK_API_KEY: str = Field(..., description="DeepSeek API密钥")
    DEEPSEEK_BASE_URL: str = Field(default="https://api.deepseek.com", description="DeepSeek API基础URL")
    
    # 企业微信配置
    WECHAT_CORP_ID: str = Field(..., description="企业微信企业ID")
    WECHAT_CORP_SECRET: str = Field(..., description="企业微信应用密钥")
    WECHAT_AGENT_ID: str = Field(..., description="企业微信应用ID")
    WECHAT_TOKEN: Optional[str] = Field(default=None, description="企业微信Webhook Token")
    WECHAT_ENCODING_AES_KEY: Optional[str] = Field(default=None, description="企业微信消息加密密钥")
    
    # 数据库配置
    DATABASE_URL: str = Field(default="sqlite:///./app.db", description="数据库连接URL")
    
    # 向量数据库配置
    FAISS_INDEX_PATH: str = Field(default="./data/faiss_index", description="FAISS索引文件路径")
    DOCUMENTS_PATH: str = Field(default="./data/documents", description="文档存储路径")
    
    # 应用配置
    APP_HOST: str = Field(default="0.0.0.0", description="应用监听地址")
    APP_PORT: int = Field(default=8000, description="应用监听端口")
    DEBUG: bool = Field(default=False, description="调试模式")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE: str = Field(default="./logs/app.log", description="日志文件路径")
    
    # AI模型配置
    EMBEDDING_MODEL: str = Field(
        default="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        description="嵌入模型名称"
    )
    MAX_CHUNK_SIZE: int = Field(default=800, description="文档分块最大大小")
    CHUNK_OVERLAP: int = Field(default=100, description="文档分块重叠大小")
    MAX_SEARCH_RESULTS: int = Field(default=5, description="最大搜索结果数量")
    SIMILARITY_THRESHOLD: float = Field(default=0.7, description="相似度阈值")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保必要的目录存在
        os.makedirs(os.path.dirname(self.FAISS_INDEX_PATH), exist_ok=True)
        os.makedirs(self.DOCUMENTS_PATH, exist_ok=True)
        os.makedirs(os.path.dirname(self.LOG_FILE), exist_ok=True)


# 创建全局配置实例
settings = Settings()
