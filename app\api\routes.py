"""
API路由定义
"""

import os
import tempfile
from typing import List
from fastapi import APIRouter, HTTPException, UploadFile, File, Request, Query
from datetime import datetime

from app.api.models import *
from app.services.qa_service import QAService
from app.services.document_service import DocumentService
from app.core.logger import logger

# 创建API路由器
api_router = APIRouter()


@api_router.post("/qa/ask", response_model=QuestionResponse)
async def ask_question(request: QuestionRequest, req: Request):
    """问答接口"""
    try:
        vector_store = req.app.state.vector_store
        qa_service = QAService(vector_store)
        
        result = await qa_service.answer_question(
            question=request.question,
            user_id=request.user_id
        )
        
        return QuestionResponse(**result)
        
    except Exception as e:
        logger.error(f"问答接口错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/qa/history", response_model=QAHistoryResponse)
async def get_qa_history(
    req: Request,
    user_id: str = Query(None, description="用户ID"),
    limit: int = Query(10, description="返回数量", ge=1, le=100)
):
    """获取问答历史"""
    try:
        vector_store = req.app.state.vector_store
        qa_service = QAService(vector_store)
        
        history = await qa_service.get_qa_history(user_id=user_id, limit=limit)
        
        return QAHistoryResponse(
            history=[QAHistoryItem(**item) for item in history],
            total=len(history)
        )
        
    except Exception as e:
        logger.error(f"获取问答历史错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/qa/popular", response_model=List[PopularQuestion])
async def get_popular_questions(
    req: Request,
    limit: int = Query(10, description="返回数量", ge=1, le=50)
):
    """获取热门问题"""
    try:
        vector_store = req.app.state.vector_store
        qa_service = QAService(vector_store)
        
        popular = await qa_service.get_popular_questions(limit=limit)
        
        return [PopularQuestion(**item) for item in popular]
        
    except Exception as e:
        logger.error(f"获取热门问题错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/documents/upload", response_model=DocumentUploadResponse)
async def upload_document(req: Request, file: UploadFile = File(...)):
    """上传文档"""
    try:
        # 验证文件类型
        allowed_extensions = {'.pdf', '.txt', '.docx', '.doc'}
        file_extension = os.path.splitext(file.filename)[1].lower()
        
        if file_extension not in allowed_extensions:
            return DocumentUploadResponse(
                success=False,
                message="不支持的文件格式",
                error=f"支持的格式: {', '.join(allowed_extensions)}"
            )
        
        # 验证文件大小（50MB限制）
        if file.size and file.size > 50 * 1024 * 1024:
            return DocumentUploadResponse(
                success=False,
                message="文件过大",
                error="文件大小不能超过50MB"
            )
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # 处理文档
            vector_store = req.app.state.vector_store
            document_service = DocumentService(vector_store)
            
            result = await document_service.upload_document(
                file_path=temp_file_path,
                filename=file.filename
            )
            
            return DocumentUploadResponse(**result)
            
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
        
    except Exception as e:
        logger.error(f"文档上传错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/documents", response_model=DocumentListResponse)
async def get_documents(
    req: Request,
    skip: int = Query(0, description="跳过数量", ge=0),
    limit: int = Query(100, description="返回数量", ge=1, le=100)
):
    """获取文档列表"""
    try:
        vector_store = req.app.state.vector_store
        document_service = DocumentService(vector_store)
        
        documents = await document_service.get_documents(skip=skip, limit=limit)
        
        return DocumentListResponse(
            documents=[DocumentInfo(**doc) for doc in documents],
            total=len(documents)
        )
        
    except Exception as e:
        logger.error(f"获取文档列表错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/documents/{document_id}", response_model=DocumentDetailResponse)
async def get_document_detail(req: Request, document_id: int):
    """获取文档详情"""
    try:
        vector_store = req.app.state.vector_store
        document_service = DocumentService(vector_store)
        
        document = await document_service.get_document_detail(document_id)
        
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return DocumentDetailResponse(**document)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档详情错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.delete("/documents/{document_id}", response_model=DeleteResponse)
async def delete_document(req: Request, document_id: int):
    """删除文档"""
    try:
        vector_store = req.app.state.vector_store
        document_service = DocumentService(vector_store)
        
        result = await document_service.delete_document(document_id)
        
        return DeleteResponse(**result)
        
    except Exception as e:
        logger.error(f"删除文档错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/documents/search", response_model=SearchResponse)
async def search_documents(req: Request, search_request: SearchRequest):
    """搜索文档"""
    try:
        vector_store = req.app.state.vector_store
        document_service = DocumentService(vector_store)
        
        results = await document_service.search_documents(
            query=search_request.query,
            limit=search_request.limit
        )
        
        return SearchResponse(
            results=[SearchResult(**result) for result in results],
            total=len(results),
            query=search_request.query
        )
        
    except Exception as e:
        logger.error(f"搜索文档错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/stats", response_model=StatsResponse)
async def get_stats(req: Request):
    """获取统计信息"""
    try:
        vector_store = req.app.state.vector_store
        qa_service = QAService(vector_store)
        document_service = DocumentService(vector_store)
        
        qa_stats = qa_service.get_stats()
        document_stats = document_service.get_stats()
        
        return StatsResponse(
            qa_stats=qa_stats,
            document_stats=document_stats
        )
        
    except Exception as e:
        logger.error(f"获取统计信息错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/health", response_model=HealthResponse)
async def health_check(req: Request):
    """健康检查"""
    try:
        # 检查各组件状态
        components = {}
        
        # 检查向量存储
        try:
            vector_store = req.app.state.vector_store
            stats = vector_store.get_stats()
            components["vector_store"] = "healthy"
        except:
            components["vector_store"] = "unhealthy"
        
        # 检查数据库
        try:
            from app.core.database import get_db
            db = next(get_db())
            db.execute("SELECT 1")
            db.close()
            components["database"] = "healthy"
        except:
            components["database"] = "unhealthy"
        
        # 检查LLM服务
        try:
            from app.services.llm_service import LLMService
            llm_service = LLMService()
            components["llm_service"] = "healthy"
        except:
            components["llm_service"] = "unhealthy"
        
        overall_status = "healthy" if all(status == "healthy" for status in components.values()) else "degraded"
        
        return HealthResponse(
            status=overall_status,
            timestamp=datetime.now().isoformat(),
            version="1.0.0",
            components=components
        )
        
    except Exception as e:
        logger.error(f"健康检查错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))
