"""
服务模块测试
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock

from app.services.document_processor import DocumentProcessor
from app.services.llm_service import LLMService
from app.core.vector_store import VectorStore


class TestDocumentProcessor:
    """文档处理器测试"""
    
    def test_validate_file_valid_pdf(self):
        """测试验证有效PDF文件"""
        # 创建临时PDF文件
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
            f.write(b'%PDF-1.4\ntest content')
            temp_path = f.name
        
        try:
            result = DocumentProcessor.validate_file(temp_path)
            assert result is True
        finally:
            os.unlink(temp_path)
    
    def test_validate_file_invalid_extension(self):
        """测试验证无效文件扩展名"""
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as f:
            f.write(b'test content')
            temp_path = f.name
        
        try:
            result = DocumentProcessor.validate_file(temp_path)
            assert result is False
        finally:
            os.unlink(temp_path)
    
    def test_validate_file_not_exists(self):
        """测试验证不存在的文件"""
        result = DocumentProcessor.validate_file('/nonexistent/file.pdf')
        assert result is False
    
    def test_extract_from_txt_utf8(self):
        """测试从UTF-8 TXT文件提取文本"""
        content = "这是测试内容\n包含中文字符"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', 
                                       encoding='utf-8', delete=False) as f:
            f.write(content)
            temp_path = f.name
        
        try:
            result = DocumentProcessor._extract_from_txt(temp_path)
            assert result == content
        finally:
            os.unlink(temp_path)
    
    def test_split_text_into_chunks(self):
        """测试文本分块"""
        text = "这是第一段内容。" * 50 + "这是第二段内容。" * 50
        
        chunks = DocumentProcessor.split_text_into_chunks(
            text, chunk_size=100, overlap=20
        )
        
        assert len(chunks) > 1
        assert all(len(chunk) <= 120 for chunk in chunks)  # 考虑重叠
    
    def test_split_text_short_content(self):
        """测试短文本分块"""
        text = "这是一段很短的内容。"
        
        chunks = DocumentProcessor.split_text_into_chunks(text, chunk_size=100)
        
        assert len(chunks) == 1
        assert chunks[0] == text
    
    def test_clean_text(self):
        """测试文本清理"""
        dirty_text = "这是   测试\n\n\n内容  包含  多余空格"
        
        clean_text = DocumentProcessor._clean_text(dirty_text)
        
        assert "   " not in clean_text
        assert "\n\n\n" not in clean_text
        assert clean_text.strip() == "这是 测试 内容 包含 多余空格"


class TestLLMService:
    """LLM服务测试"""
    
    @pytest.fixture
    def llm_service(self):
        """LLM服务实例"""
        return LLMService()
    
    @patch('app.services.llm_service.OpenAI')
    @pytest.mark.asyncio
    async def test_generate_answer_success(self, mock_openai, llm_service):
        """测试生成回答成功"""
        # 模拟OpenAI响应
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "这是测试回答"
        
        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        # 重新初始化服务以使用模拟客户端
        llm_service.client = mock_client
        
        context_chunks = [
            {
                "content": "测试法规内容",
                "filename": "test.pdf",
                "similarity_score": 0.9
            }
        ]
        
        result = await llm_service.generate_answer(
            question="测试问题",
            context_chunks=context_chunks,
            user_id="test_user"
        )
        
        assert result["success"] is True
        assert "这是测试回答" in result["answer"]
        assert len(result["sources"]) == 1
    
    @patch('app.services.llm_service.OpenAI')
    @pytest.mark.asyncio
    async def test_generate_answer_api_error(self, mock_openai, llm_service):
        """测试API调用错误"""
        mock_client = Mock()
        mock_client.chat.completions.create.side_effect = Exception("API错误")
        mock_openai.return_value = mock_client
        
        llm_service.client = mock_client
        
        result = await llm_service.generate_answer(
            question="测试问题",
            context_chunks=[],
            user_id="test_user"
        )
        
        assert result["success"] is False
        assert "error" in result
    
    def test_extract_sources(self, llm_service):
        """测试提取引用来源"""
        context_chunks = [
            {
                "filename": "test1.pdf",
                "content": "这是第一个文档的内容" * 10,
                "similarity_score": 0.9
            },
            {
                "filename": "test2.pdf",
                "content": "这是第二个文档的内容",
                "similarity_score": 0.8
            }
        ]
        
        sources = llm_service._extract_sources(context_chunks)
        
        assert len(sources) == 2
        assert sources[0]["filename"] == "test1.pdf"
        assert sources[0]["similarity_score"] == 0.9
        assert "..." in sources[0]["content_preview"]  # 长内容被截断
        assert sources[1]["content_preview"] == "这是第二个文档的内容"  # 短内容不截断
    
    def test_format_answer_with_disclaimer(self, llm_service):
        """测试格式化回答（添加免责声明）"""
        answer = "这是一个测试回答"
        
        formatted = llm_service._format_answer(answer, [])
        
        assert "仅供参考" in formatted or "免责" in formatted
    
    def test_format_answer_already_has_disclaimer(self, llm_service):
        """测试格式化已包含免责声明的回答"""
        answer = "这是一个测试回答，仅供参考"
        
        formatted = llm_service._format_answer(answer, [])
        
        # 不应该重复添加免责声明
        assert formatted.count("仅供参考") == 1


class TestVectorStore:
    """向量存储测试"""
    
    @pytest.fixture
    def vector_store(self):
        """向量存储实例"""
        return VectorStore()
    
    @patch('app.core.vector_store.SentenceTransformer')
    @patch('app.core.vector_store.faiss')
    @pytest.mark.asyncio
    async def test_initialize_success(self, mock_faiss, mock_transformer, vector_store):
        """测试初始化成功"""
        # 模拟sentence transformer
        mock_model = Mock()
        mock_model.get_sentence_embedding_dimension.return_value = 384
        mock_transformer.return_value = mock_model
        
        # 模拟faiss索引
        mock_index = Mock()
        mock_index.ntotal = 0
        mock_faiss.IndexFlatIP.return_value = mock_index
        
        await vector_store.initialize()
        
        assert vector_store.model is not None
        assert vector_store.dimension == 384
        assert vector_store.index is not None
    
    @patch('app.core.vector_store.SentenceTransformer')
    @patch('app.core.vector_store.faiss')
    @pytest.mark.asyncio
    async def test_add_texts_success(self, mock_faiss, mock_transformer, vector_store):
        """测试添加文本成功"""
        # 设置模拟
        mock_model = Mock()
        mock_model.encode.return_value = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]
        vector_store.model = mock_model
        
        mock_index = Mock()
        mock_index.ntotal = 0
        vector_store.index = mock_index
        vector_store.dimension = 3
        
        # 模拟faiss normalize
        mock_faiss.normalize_L2 = Mock()
        
        texts = ["文本1", "文本2"]
        metadatas = [{"doc_id": 1}, {"doc_id": 2}]
        
        indices = await vector_store.add_texts(texts, metadatas)
        
        assert len(indices) == 2
        assert len(vector_store.texts) == 2
        assert len(vector_store.metadata) == 2
    
    @patch('app.core.vector_store.SentenceTransformer')
    @patch('app.core.vector_store.faiss')
    @pytest.mark.asyncio
    async def test_search_success(self, mock_faiss, mock_transformer, vector_store):
        """测试搜索成功"""
        # 设置模拟
        mock_model = Mock()
        mock_model.encode.return_value = [[0.1, 0.2, 0.3]]
        vector_store.model = mock_model
        
        mock_index = Mock()
        mock_index.ntotal = 2
        mock_index.search.return_value = ([[0.9, 0.8]], [[0, 1]])
        vector_store.index = mock_index
        
        vector_store.texts = ["文本1", "文本2"]
        vector_store.metadata = [{"doc_id": 1}, {"doc_id": 2}]
        
        # 模拟faiss normalize
        mock_faiss.normalize_L2 = Mock()
        
        results = await vector_store.search("查询文本", k=2, threshold=0.7)
        
        assert len(results) == 2
        assert results[0][1] == 0.9  # 相似度分数
        assert results[0][2]["doc_id"] == 1  # 元数据
    
    def test_get_stats(self, vector_store):
        """测试获取统计信息"""
        # 设置模拟数据
        mock_index = Mock()
        mock_index.ntotal = 100
        vector_store.index = mock_index
        vector_store.dimension = 384
        vector_store.texts = ["text"] * 100
        vector_store.metadata = [{}] * 100
        
        stats = vector_store.get_stats()
        
        assert stats["total_vectors"] == 100
        assert stats["dimension"] == 384
        assert stats["texts_count"] == 100
        assert stats["metadata_count"] == 100


if __name__ == "__main__":
    pytest.main([__file__])
