"""
大语言模型服务 - DeepSeek API调用
"""

import json
from typing import List, Dict, Optional
from openai import OpenAI

from app.core.config import settings
from app.core.logger import logger


class LLMService:
    """大语言模型服务"""
    
    def __init__(self):
        self.client = OpenAI(
            api_key=settings.DEEPSEEK_API_KEY,
            base_url=settings.DEEPSEEK_BASE_URL
        )
        self.model = "deepseek-chat"  # DeepSeek的聊天模型
    
    async def generate_answer(self, question: str, context_chunks: List[Dict], 
                            user_id: str = None) -> Dict:
        """生成法规问答回答"""
        try:
            # 构建提示词
            prompt = self._build_legal_qa_prompt(question, context_chunks)
            
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.3,  # 较低的温度确保回答的一致性
                max_tokens=1000,
                top_p=0.9
            )
            
            answer = response.choices[0].message.content.strip()
            
            # 格式化回答
            formatted_answer = self._format_answer(answer, context_chunks)
            
            logger.info(f"DeepSeek API调用成功，用户: {user_id}, 问题长度: {len(question)}")
            
            return {
                "answer": formatted_answer,
                "sources": self._extract_sources(context_chunks),
                "model": self.model,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            return {
                "answer": "抱歉，系统暂时无法处理您的问题，请稍后重试。",
                "sources": [],
                "model": self.model,
                "success": False,
                "error": str(e)
            }
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的法规问答助手，专门帮助用户解答法律法规相关问题。

请遵循以下原则：
1. 基于提供的法规文档内容进行回答
2. 回答要准确、专业、简洁明了
3. 必须引用具体的法规条文作为依据
4. 如果文档中没有相关信息，请明确说明
5. 提供实用的建议和指导
6. 始终添加免责声明

回答格式要求：
- 先给出明确的结论
- 然后列出法规依据
- 最后提供建议
- 结尾添加免责声明"""
    
    def _build_legal_qa_prompt(self, question: str, context_chunks: List[Dict]) -> str:
        """构建法规问答提示词"""
        # 整理上下文
        context_text = ""
        for i, chunk in enumerate(context_chunks, 1):
            source_info = f"[文档{i}: {chunk.get('filename', '未知文档')}]"
            context_text += f"{source_info}\n{chunk.get('content', '')}\n\n"
        
        prompt = f"""基于以下法规文档内容，回答用户的问题：

相关法规文档：
{context_text}

用户问题：{question}

请根据上述法规文档内容，提供专业的回答。如果文档中没有直接相关的信息，请说明并提供一般性建议。"""
        
        return prompt
    
    def _format_answer(self, answer: str, context_chunks: List[Dict]) -> str:
        """格式化回答"""
        # 确保回答包含免责声明
        if "仅供参考" not in answer and "免责" not in answer:
            answer += "\n\n⚠️ *此回答仅供参考，具体情况请咨询专业法律顾问*"
        
        return answer
    
    def _extract_sources(self, context_chunks: List[Dict]) -> List[Dict]:
        """提取引用来源"""
        sources = []
        for chunk in context_chunks:
            source = {
                "filename": chunk.get("filename", "未知文档"),
                "content_preview": chunk.get("content", "")[:100] + "..." if len(chunk.get("content", "")) > 100 else chunk.get("content", ""),
                "similarity_score": chunk.get("similarity_score", 0.0)
            }
            sources.append(source)
        return sources
    
    async def summarize_document(self, content: str, filename: str) -> str:
        """文档摘要生成"""
        try:
            prompt = f"""请为以下法规文档生成简洁的摘要：

文档名称：{filename}
文档内容：
{content[:2000]}...

请生成一个200字以内的摘要，包括：
1. 文档的主要内容
2. 关键条款
3. 适用范围"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的法规文档分析助手，擅长生成准确简洁的文档摘要。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=300
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"文档摘要生成失败: {e}")
            return f"《{filename}》- 摘要生成失败"
    
    async def extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        try:
            prompt = f"""从以下文本中提取5-10个最重要的关键词：

{text[:1000]}

请只返回关键词列表，用逗号分隔。"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=100
            )
            
            keywords_text = response.choices[0].message.content.strip()
            keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
            
            return keywords[:10]  # 最多返回10个关键词
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
