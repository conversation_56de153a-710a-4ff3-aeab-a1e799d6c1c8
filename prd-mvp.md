# 产品文档：企微群 AI 法规问答机器人 (MVP版本)

## 一、产品简介

本产品是一款嵌入企业微信群的 AI 智能问答机器人MVP版本，支持用户通过 @机器人 的方式，结合预设的法规文档，快速获得合规性问答。机器人基于通义千问大语言模型并集成本地向量检索（RAG）能力，为企业提供便捷的法规咨询服务。

**MVP目标**：快速验证核心价值，以最小成本实现基础法规问答功能。

---

## 二、核心功能（MVP范围）

### 1. 基础问答功能
* 群成员可 @机器人 提问法规相关问题
* 支持基于预设法规文档的合规性分析
* 提供简洁明确的回答，包含相关法规引用

### 2. 文档处理（简化版）
* **支持格式**：PDF、TXT、word文件
* **处理方式**：文档分块存储到本地向量数据库
* **上传方式**：管理员通过简单接口批量上传

### 3. 智能检索回答
* 用户问题自动向量化检索相关法规条文
* 结合检索结果调用deepseek生成专业回答
* 回答包含法规条文引用和简要分析

---

## 三、技术架构（简化版）

```text
[企业微信群] → [Webhook接收] → [Flask/FastAPI服务] →
   ├─ 本地FAISS向量库
   ├─ 文档解析（PyPDF2 + 文本处理）
   ├─ 中文Embedding模型（sentence-transformers）
   └─ deepseekAPI调用
→ [返回群聊回答]
```

**技术选型**：
- **后端框架**：Flask/FastAPI
- **向量数据库**：本地FAISS
- **文档解析**：PyPDF2（PDF）+ 基础文本处理
- **Embedding模型**：sentence-transformers中文模型
- **大语言模型**：通义千问API（成本较低）
- **数据存储**：SQLite（元数据）+ 本地文件系统
- **部署方式**：Docker容器化单机部署

---

## 四、使用流程（简化版）

1. **系统初始化**：管理员上传法规文档到系统
2. **机器人入群**：将机器人添加到企业微信群
3. **用户提问**：群成员 @机器人 + 问题
4. **智能回答**：机器人检索相关法规并生成回答
5. **持续优化**：根据使用反馈调整回答质量

---

## 五、使用示例

> **用户提问**：@机器人 员工加班超过36小时是否违反劳动法？
>
> **🤖 机器人回复**：
>
> 根据相关法规分析：
>
> **违反规定**：员工加班超过36小时违反《劳动法》相关规定。
>
> **法规依据**：
> - 《劳动法》第41条：延长工作时间每日不得超过3小时，每月不得超过36小时
>
> **建议**：企业应合理安排工作时间，确保员工加班时长符合法律规定。
>
> ⚠️ *此回答仅供参考，具体情况请咨询法律顾问*

---

## 六、MVP功能边界

### ✅ **包含功能**
- 企业微信群基础问答
- PDF/TXT文档解析和检索
- 基于通义千问的智能回答
- 简单的法规条文引用
- 基础的管理员文档上传

### ❌ **暂不包含**
- Word文档支持
- 复杂的后台管理界面
- 多轮对话记忆
- 文档版本管理
- 用户权限管理
- 高级分析功能
- 自动法规更新

---

## 七、技术实现要点

### 1. 文档处理策略
- **分块大小**：500-800字符，重叠100字符
- **向量化**：使用中文优化的sentence-transformers模型
- **存储格式**：文档ID + 分块内容 + 向量 + 元数据

### 2. 检索策略
- **相似度阈值**：0.7以上的相关片段
- **返回数量**：Top 3-5个最相关片段
- **上下文长度**：控制在4000字符以内

### 3. 回答生成
- **Prompt模板**：结构化的法规问答模板
- **回答格式**：结论 + 法规依据 + 建议 + 免责声明
- **长度控制**：回答控制在500字以内

---

## 八、部署和运维

### 1. 部署要求
- **服务器配置**：2核4G内存，50G存储
- **运行环境**：Docker + Python 3.9+
- **网络要求**：支持HTTPS，可访问通义千问API

### 2. 成本估算
- **服务器成本**：约200-500元/月
- **API调用成本**：根据使用量，预估100-300元/月
- **总成本**：约300-800元/月

---

## 九、开发计划

### 第一阶段（2-3周）：核心功能开发
- [ ] 企业微信接入和Webhook处理
- [ ] 文档解析和向量化模块
- [ ] 基础问答功能实现
- [ ] deepseek API集成

### 第二阶段（1-2周）：测试和优化
- [ ] 功能测试和bug修复
- [ ] 回答质量调优
- [ ] 性能优化和部署

### 第三阶段（1周）：上线和监控
- [ ] 生产环境部署
- [ ] 监控和日志系统
- [ ] 用户培训和文档

---

## 十、成功指标

### 1. 功能指标
- 问答响应时间 < 10秒
- 回答准确率 > 80%（人工评估）
- 系统可用性 > 99%

### 2. 业务指标
- 日活跃用户 > 10人
- 日均问答次数 > 20次
- 用户满意度 > 4.0/5.0

### 3. 技术指标
- API调用成功率 > 99%
- 文档检索响应时间 < 2秒
- 系统内存使用率 < 80%

---

## 十一、风险和限制

### 1. 技术风险
- **准确性风险**：AI回答可能存在错误，需要免责声明
- **API依赖**：依赖第三方API服务稳定性
- **性能瓶颈**：单机部署可能存在并发限制

### 2. 业务风险
- **法律责任**：明确机器人回答仅供参考
- **数据安全**：确保上传文档的安全性
- **用户接受度**：需要用户培训和适应过程

### 3. 应对措施
- 添加明确的免责声明
- 建立人工复核机制
- 定期备份和监控
- 收集用户反馈持续改进

---

## 十二、详细技术方案

### 1. 企业微信接入方案
```python
# 企业微信Webhook接收示例
@app.route('/wechat/webhook', methods=['POST'])
def wechat_webhook():
    data = request.get_json()
    if data.get('MsgType') == 'text' and '@机器人' in data.get('Content', ''):
        question = extract_question(data['Content'])
        answer = process_question(question)
        send_wechat_message(data['FromUserName'], answer)
    return 'success'
```

### 2. 文档处理流程
```python
# 文档处理核心逻辑
def process_document(file_path):
    # 1. 文档解析
    content = extract_text(file_path)

    # 2. 文本分块
    chunks = split_text(content, chunk_size=600, overlap=100)

    # 3. 向量化
    embeddings = model.encode(chunks)

    # 4. 存储到FAISS
    faiss_index.add(embeddings)
    save_metadata(chunks, file_path)
```

### 3. 问答处理流程
```python
# 问答核心逻辑
def process_question(question):
    # 1. 问题向量化
    question_embedding = model.encode([question])

    # 2. 相似度检索
    scores, indices = faiss_index.search(question_embedding, k=5)

    # 3. 构建上下文
    context = build_context(indices, scores)

    # 4. 调用LLM
    prompt = build_prompt(question, context)
    answer = call_tongyi_api(prompt)

    return format_answer(answer)
```

---

## 十三、数据结构设计

### 1. 文档元数据表（SQLite）
```sql
CREATE TABLE documents (
    id INTEGER PRIMARY KEY,
    filename VARCHAR(255),
    file_path VARCHAR(500),
    upload_time DATETIME,
    file_size INTEGER,
    chunk_count INTEGER,
    status VARCHAR(50)
);

CREATE TABLE document_chunks (
    id INTEGER PRIMARY KEY,
    document_id INTEGER,
    chunk_index INTEGER,
    content TEXT,
    embedding_index INTEGER,
    FOREIGN KEY (document_id) REFERENCES documents(id)
);
```

### 2. 问答记录表
```sql
CREATE TABLE qa_records (
    id INTEGER PRIMARY KEY,
    user_id VARCHAR(100),
    question TEXT,
    answer TEXT,
    related_docs TEXT,
    created_time DATETIME,
    response_time FLOAT
);
```

---

## 十四、API接口设计

### 1. 文档管理接口
```
POST /api/documents/upload
- 功能：上传文档
- 参数：file (multipart/form-data)
- 返回：{"status": "success", "document_id": 123}

GET /api/documents/list
- 功能：获取文档列表
- 返回：{"documents": [...]}

DELETE /api/documents/{id}
- 功能：删除文档
- 返回：{"status": "success"}
```

### 2. 问答接口
```
POST /api/qa/ask
- 功能：问答接口
- 参数：{"question": "问题内容"}
- 返回：{"answer": "回答内容", "sources": [...]}
```

---

## 十五、部署配置

### 1. Docker配置
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. 环境变量配置
```bash
# .env文件
TONGYI_API_KEY=your_api_key
WECHAT_CORP_ID=your_corp_id
WECHAT_CORP_SECRET=your_corp_secret
DATABASE_URL=sqlite:///./app.db
FAISS_INDEX_PATH=./faiss_index
```

---

## 十六、测试策略

### 1. 单元测试
- 文档解析功能测试
- 向量检索功能测试
- LLM调用功能测试
- 企业微信接口测试

### 2. 集成测试
- 端到端问答流程测试
- 文档上传和处理流程测试
- 错误处理和异常情况测试

### 3. 性能测试
- 并发用户测试（10-50用户）
- 响应时间测试（目标<10秒）
- 内存使用测试

---

## 十七、监控和日志

### 1. 关键指标监控
- API响应时间
- 错误率统计
- 用户活跃度
- 资源使用情况

### 2. 日志记录
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

# 记录关键操作
logger.info(f"User {user_id} asked: {question}")
logger.info(f"Response time: {response_time}s")
```

---

## 十八、后续迭代规划

### V1.1 版本（MVP后1-2个月）
- [ ] 添加Word文档支持
- [ ] 优化回答质量和格式
- [ ] 添加简单的Web管理界面
- [ ] 支持文档标签和分类

### V1.2 版本（3-4个月）
- [ ] 多轮对话支持
- [ ] 用户反馈和评分系统
- [ ] 更丰富的回答格式
- [ ] 性能优化和扩展

### V2.0 版本（6个月后）
- [ ] 分布式部署支持
- [ ] 高级分析功能
- [ ] 自动法规更新
- [ ] 企业级权限管理

这个MVP版本的PRD专注于核心功能，大幅简化了技术复杂度，预计4-6周可以完成开发和测试，是一个非常实用的起步方案。
