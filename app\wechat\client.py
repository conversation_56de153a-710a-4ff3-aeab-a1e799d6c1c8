"""
企业微信客户端 - 处理消息发送和接收
"""

import json
import time
import hashlib
import requests
from typing import Dict, Optional
from urllib.parse import quote

from app.core.config import settings
from app.core.logger import logger


class WeChatClient:
    """企业微信客户端"""
    
    def __init__(self):
        self.corp_id = settings.WECHAT_CORP_ID
        self.corp_secret = settings.WECHAT_CORP_SECRET
        self.agent_id = settings.WECHAT_AGENT_ID
        self.access_token = None
        self.token_expires_at = 0
        
        # API端点
        self.base_url = "https://qyapi.weixin.qq.com/cgi-bin"
    
    async def get_access_token(self) -> str:
        """获取访问令牌"""
        try:
            # 检查token是否过期
            if self.access_token and time.time() < self.token_expires_at:
                return self.access_token
            
            # 获取新token
            url = f"{self.base_url}/gettoken"
            params = {
                "corpid": self.corp_id,
                "corpsecret": self.corp_secret
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get("errcode") != 0:
                raise Exception(f"获取access_token失败: {data.get('errmsg')}")
            
            self.access_token = data["access_token"]
            # 提前5分钟过期
            self.token_expires_at = time.time() + data["expires_in"] - 300
            
            logger.info("企业微信access_token获取成功")
            return self.access_token
            
        except Exception as e:
            logger.error(f"获取企业微信access_token失败: {e}")
            raise
    
    async def send_text_message(self, user_id: str, content: str, 
                               safe: int = 0) -> Dict:
        """发送文本消息"""
        try:
            access_token = await self.get_access_token()
            
            url = f"{self.base_url}/message/send"
            params = {"access_token": access_token}
            
            data = {
                "touser": user_id,
                "msgtype": "text",
                "agentid": self.agent_id,
                "text": {
                    "content": content
                },
                "safe": safe
            }
            
            response = requests.post(
                url, 
                params=params, 
                json=data, 
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("errcode") != 0:
                logger.error(f"发送消息失败: {result.get('errmsg')}")
                return {
                    "success": False,
                    "error": result.get("errmsg")
                }
            
            logger.info(f"消息发送成功，用户: {user_id}")
            return {
                "success": True,
                "msgid": result.get("msgid")
            }
            
        except Exception as e:
            logger.error(f"发送企业微信消息失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def send_markdown_message(self, user_id: str, content: str) -> Dict:
        """发送Markdown消息"""
        try:
            access_token = await self.get_access_token()
            
            url = f"{self.base_url}/message/send"
            params = {"access_token": access_token}
            
            data = {
                "touser": user_id,
                "msgtype": "markdown",
                "agentid": self.agent_id,
                "markdown": {
                    "content": content
                }
            }
            
            response = requests.post(
                url, 
                params=params, 
                json=data, 
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("errcode") != 0:
                logger.error(f"发送Markdown消息失败: {result.get('errmsg')}")
                return {
                    "success": False,
                    "error": result.get("errmsg")
                }
            
            logger.info(f"Markdown消息发送成功，用户: {user_id}")
            return {
                "success": True,
                "msgid": result.get("msgid")
            }
            
        except Exception as e:
            logger.error(f"发送企业微信Markdown消息失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_user_info(self, user_id: str) -> Dict:
        """获取用户信息"""
        try:
            access_token = await self.get_access_token()
            
            url = f"{self.base_url}/user/get"
            params = {
                "access_token": access_token,
                "userid": user_id
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("errcode") != 0:
                logger.error(f"获取用户信息失败: {result.get('errmsg')}")
                return {}
            
            return {
                "userid": result.get("userid"),
                "name": result.get("name"),
                "department": result.get("department", []),
                "position": result.get("position"),
                "mobile": result.get("mobile"),
                "email": result.get("email")
            }
            
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return {}
    
    def verify_signature(self, signature: str, timestamp: str, nonce: str, 
                        token: str) -> bool:
        """验证签名"""
        try:
            # 将token、timestamp、nonce三个参数进行字典序排序
            tmp_list = [token, timestamp, nonce]
            tmp_list.sort()
            
            # 将三个参数字符串拼接成一个字符串进行sha1加密
            tmp_str = ''.join(tmp_list)
            signature_calculated = hashlib.sha1(tmp_str.encode()).hexdigest()
            
            return signature == signature_calculated
            
        except Exception as e:
            logger.error(f"验证签名失败: {e}")
            return False
    
    def format_legal_answer(self, answer: str, sources: list) -> str:
        """格式化法规问答回答为企业微信消息格式"""
        try:
            # 基础回答
            formatted_message = f"🤖 **法规问答助手**\n\n{answer}\n\n"
            
            # 添加引用来源
            if sources:
                formatted_message += "📚 **参考文档**：\n"
                for i, source in enumerate(sources[:3], 1):  # 最多显示3个来源
                    filename = source.get("filename", "未知文档")
                    score = source.get("similarity_score", 0)
                    formatted_message += f"{i}. {filename} (相关度: {score:.2f})\n"
            
            # 添加时间戳
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            formatted_message += f"\n⏰ {current_time}"
            
            return formatted_message
            
        except Exception as e:
            logger.error(f"格式化消息失败: {e}")
            return answer  # 返回原始回答作为备选
    
    async def send_typing_indicator(self, user_id: str):
        """发送正在输入指示器（如果支持）"""
        # 企业微信暂不支持输入指示器，这里可以发送一个提示消息
        try:
            await self.send_text_message(
                user_id=user_id,
                content="🤔 正在思考中，请稍候..."
            )
        except Exception as e:
            logger.warning(f"发送输入指示器失败: {e}")
    
    def extract_question_from_message(self, content: str) -> Optional[str]:
        """从消息中提取问题"""
        try:
            # 移除@机器人的部分
            content = content.strip()
            
            # 常见的@机器人格式
            bot_mentions = ["@机器人", "@法规助手", "@AI助手", "@bot"]
            
            for mention in bot_mentions:
                if mention in content:
                    content = content.replace(mention, "").strip()
            
            # 如果内容为空或太短，返回None
            if not content or len(content.strip()) < 2:
                return None
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"提取问题失败: {e}")
            return None
