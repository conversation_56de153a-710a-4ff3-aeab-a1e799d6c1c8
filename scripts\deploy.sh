#!/bin/bash

# 企微群 AI 法规问答机器人安全部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "🚀 开始部署企微群 AI 法规问答机器人..."

# 安全检查函数
security_check() {
    echo "🔒 执行安全检查..."

    # 检查文件权限
    if [ -f ".env" ]; then
        chmod 600 .env
        echo "✅ 设置.env文件权限为600"
    fi

    # 检查Docker版本
    DOCKER_VERSION=$(docker --version | grep -oE '[0-9]+\.[0-9]+' | head -1)
    echo "📋 Docker版本: $DOCKER_VERSION"

    # 运行安全扫描（如果脚本存在）
    if [ -f "scripts/security-scan.sh" ]; then
        echo "🔍 运行安全扫描..."
        chmod +x scripts/security-scan.sh
        ./scripts/security-scan.sh || echo -e "${YELLOW}⚠️  安全扫描完成，请查看报告${NC}"
    fi
}

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️  未找到.env文件，从.env.example复制..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ 已创建.env文件，请编辑配置后重新运行部署脚本"
        echo "📝 需要配置的关键参数："
        echo "   - DEEPSEEK_API_KEY: DeepSeek API密钥"
        echo "   - WECHAT_CORP_ID: 企业微信企业ID"
        echo "   - WECHAT_CORP_SECRET: 企业微信应用密钥"
        echo "   - WECHAT_AGENT_ID: 企业微信应用ID"
        exit 1
    else
        echo "❌ 未找到.env.example文件"
        exit 1
    fi
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data/faiss_index
mkdir -p data/documents
mkdir -p logs
mkdir -p ssl

# 设置目录权限
chmod 755 data
chmod 755 logs

# 检查关键环境变量
source .env

if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "❌ 请在.env文件中设置DEEPSEEK_API_KEY"
    exit 1
fi

if [ -z "$WECHAT_CORP_ID" ]; then
    echo "❌ 请在.env文件中设置WECHAT_CORP_ID"
    exit 1
fi

# 执行安全检查
security_check

# 构建镜像
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 检查服务状态..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "🌐 API地址: http://localhost:8000"
    echo "📊 健康检查: http://localhost:8000/health"
    echo "📚 API文档: http://localhost:8000/docs"
else
    echo "❌ 服务启动失败，请检查日志："
    echo "docker-compose logs wecombot"
    exit 1
fi

# 显示日志
echo "📋 显示最近的日志："
docker-compose logs --tail=20 wecombot

echo ""
echo -e "${GREEN}🎉 部署完成！${NC}"
echo ""
echo "📖 使用说明："
echo "1. 访问 http://localhost:8000/docs 查看API文档"
echo "2. 使用 /api/documents/upload 上传法规文档"
echo "3. 配置企业微信应用回调地址为: http://your-domain.com/wechat/callback"
echo "4. 查看日志: docker-compose logs -f wecombot"
echo "5. 停止服务: docker-compose down"
echo ""
echo -e "${YELLOW}🔒 安全提醒：${NC}"
echo "- 服务已绑定到localhost，通过Nginx代理访问"
echo "- 请定期运行 ./scripts/security-scan.sh 进行安全检查"
echo "- 生产环境请配置HTTPS和防火墙"
echo "- 定期备份data目录中的数据"
echo "- 监控日志文件中的异常活动"
echo ""
echo -e "${GREEN}✅ 安全配置已启用：${NC}"
echo "- 非root用户运行容器"
echo "- 资源限制和安全选项已配置"
echo "- 网络隔离已启用"
echo "- 文件权限已正确设置"
