#!/bin/bash

# 测试脚本

set -e

echo "🧪 开始运行测试..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    exit 1
fi

# 检查虚拟环境
if [ -z "$VIRTUAL_ENV" ]; then
    echo "⚠️  建议在虚拟环境中运行测试"
    echo "创建虚拟环境: python3 -m venv venv"
    echo "激活虚拟环境: source venv/bin/activate"
fi

# 安装测试依赖
echo "📦 安装依赖..."
pip install -r requirements.txt

# 设置测试环境变量
export DEEPSEEK_API_KEY="test_key"
export WECHAT_CORP_ID="test_corp_id"
export WECHAT_CORP_SECRET="test_secret"
export WECHAT_AGENT_ID="test_agent_id"
export DATABASE_URL="sqlite:///./test.db"
export FAISS_INDEX_PATH="./test_data/faiss_index"
export DOCUMENTS_PATH="./test_data/documents"

# 创建测试目录
mkdir -p test_data/faiss_index
mkdir -p test_data/documents

# 运行单元测试
echo "🔬 运行单元测试..."
python -m pytest tests/ -v --tb=short

# 运行代码风格检查
echo "🎨 检查代码风格..."
if command -v black &> /dev/null; then
    black --check app/ tests/ main.py
    echo "✅ 代码风格检查通过"
else
    echo "⚠️  未安装black，跳过代码风格检查"
fi

# 清理测试数据
echo "🧹 清理测试数据..."
rm -rf test_data/
rm -f test.db

echo "✅ 所有测试完成！"
