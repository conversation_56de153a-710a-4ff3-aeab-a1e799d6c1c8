"""
API接口测试
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from main import app
from app.core.vector_store import VectorStore


@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_vector_store():
    """模拟向量存储"""
    mock_store = Mock(spec=VectorStore)
    mock_store.search.return_value = [
        ("测试法规内容", 0.9, {"filename": "test.pdf", "document_id": 1})
    ]
    mock_store.get_stats.return_value = {
        "total_vectors": 100,
        "dimension": 384
    }
    return mock_store


class TestHealthAPI:
    """健康检查API测试"""
    
    def test_root_endpoint(self, client):
        """测试根路径"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "企微群 AI 法规问答机器人"
        assert data["version"] == "1.0.0"
        assert data["status"] == "running"
    
    def test_health_endpoint(self, client):
        """测试健康检查"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"


class TestQAAPI:
    """问答API测试"""
    
    @patch('app.services.qa_service.QAService')
    def test_ask_question_success(self, mock_qa_service, client):
        """测试问答成功"""
        # 模拟QA服务返回
        mock_qa_service.return_value.answer_question.return_value = {
            "answer": "这是测试回答",
            "sources": [{"filename": "test.pdf"}],
            "response_time": 1.5,
            "success": True
        }
        
        # 模拟向量存储
        with patch.object(app.state, 'vector_store', Mock()):
            response = client.post("/api/qa/ask", json={
                "question": "测试问题",
                "user_id": "test_user"
            })
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["answer"] == "这是测试回答"
        assert len(data["sources"]) == 1
    
    def test_ask_question_invalid_input(self, client):
        """测试无效输入"""
        response = client.post("/api/qa/ask", json={
            "question": "",  # 空问题
            "user_id": "test_user"
        })
        assert response.status_code == 422  # 验证错误
    
    def test_ask_question_missing_field(self, client):
        """测试缺少必要字段"""
        response = client.post("/api/qa/ask", json={
            "user_id": "test_user"
            # 缺少question字段
        })
        assert response.status_code == 422


class TestDocumentAPI:
    """文档API测试"""
    
    @patch('app.services.document_service.DocumentService')
    def test_get_documents(self, mock_doc_service, client):
        """测试获取文档列表"""
        mock_doc_service.return_value.get_documents.return_value = [
            {
                "id": 1,
                "filename": "test.pdf",
                "file_size": 1024,
                "chunk_count": 5,
                "status": "active",
                "upload_time": "2024-01-01T00:00:00"
            }
        ]
        
        with patch.object(app.state, 'vector_store', Mock()):
            response = client.get("/api/documents")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["documents"]) == 1
        assert data["documents"][0]["filename"] == "test.pdf"
    
    def test_upload_document_invalid_format(self, client):
        """测试上传不支持的文件格式"""
        # 创建一个假的文件
        files = {"file": ("test.xyz", b"fake content", "application/octet-stream")}
        
        response = client.post("/api/documents/upload", files=files)
        assert response.status_code == 200  # 接口返回200但success为False
        data = response.json()
        assert data["success"] is False
        assert "不支持的文件格式" in data["message"]
    
    @patch('app.services.document_service.DocumentService')
    def test_delete_document_success(self, mock_doc_service, client):
        """测试删除文档成功"""
        mock_doc_service.return_value.delete_document.return_value = {
            "success": True,
            "message": "文档删除成功"
        }
        
        with patch.object(app.state, 'vector_store', Mock()):
            response = client.delete("/api/documents/1")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "文档删除成功"
    
    @patch('app.services.document_service.DocumentService')
    def test_delete_document_not_found(self, mock_doc_service, client):
        """测试删除不存在的文档"""
        mock_doc_service.return_value.delete_document.return_value = {
            "success": False,
            "error": "文档不存在"
        }
        
        with patch.object(app.state, 'vector_store', Mock()):
            response = client.delete("/api/documents/999")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False
        assert data["error"] == "文档不存在"


class TestSearchAPI:
    """搜索API测试"""
    
    @patch('app.services.document_service.DocumentService')
    def test_search_documents(self, mock_doc_service, client):
        """测试文档搜索"""
        mock_doc_service.return_value.search_documents.return_value = [
            {
                "document_id": 1,
                "filename": "test.pdf",
                "content_preview": "这是测试内容...",
                "similarity_score": 0.85,
                "chunk_index": 0
            }
        ]
        
        with patch.object(app.state, 'vector_store', Mock()):
            response = client.post("/api/documents/search", json={
                "query": "测试搜索",
                "limit": 10
            })
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["results"]) == 1
        assert data["results"][0]["filename"] == "test.pdf"
        assert data["query"] == "测试搜索"
    
    def test_search_documents_invalid_input(self, client):
        """测试搜索无效输入"""
        response = client.post("/api/documents/search", json={
            "query": "",  # 空查询
            "limit": 10
        })
        assert response.status_code == 422


class TestStatsAPI:
    """统计API测试"""
    
    @patch('app.services.qa_service.QAService')
    @patch('app.services.document_service.DocumentService')
    def test_get_stats(self, mock_doc_service, mock_qa_service, client):
        """测试获取统计信息"""
        mock_qa_service.return_value.get_stats.return_value = {
            "total_questions": 100,
            "today_questions": 10,
            "avg_response_time": 2.5
        }
        
        mock_doc_service.return_value.get_stats.return_value = {
            "total_documents": 20,
            "active_documents": 18,
            "total_chunks": 500
        }
        
        with patch.object(app.state, 'vector_store', Mock()):
            response = client.get("/api/stats")
        
        assert response.status_code == 200
        data = response.json()
        assert "qa_stats" in data
        assert "document_stats" in data
        assert data["qa_stats"]["total_questions"] == 100
        assert data["document_stats"]["total_documents"] == 20


if __name__ == "__main__":
    pytest.main([__file__])
