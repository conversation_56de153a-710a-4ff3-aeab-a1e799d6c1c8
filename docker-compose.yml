version: '3.8'

services:
  wecombot:
    build: .
    container_name: wecombot
    ports:
      - "8000:8000"
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_BASE_URL=${DEEPSEEK_BASE_URL:-https://api.deepseek.com}
      - WECHAT_CORP_ID=${WECHAT_CORP_ID}
      - WECHAT_CORP_SECRET=${WECHAT_CORP_SECRET}
      - WECHAT_AGENT_ID=${WECHAT_AGENT_ID}
      - WECHAT_TOKEN=${WECHAT_TOKEN}
      - WECHAT_ENCODING_AES_KEY=${WECHAT_ENCODING_AES_KEY}
      - DATABASE_URL=sqlite:///./app.db
      - FAISS_INDEX_PATH=./data/faiss_index
      - DOCUMENTS_PATH=./data/documents
      - APP_HOST=0.0.0.0
      - APP_PORT=8000
      - DEBUG=${DEBUG:-False}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE=./logs/app.log
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./app.db:/app/app.db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: wecombot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - wecombot
    restart: unless-stopped
    profiles:
      - production

networks:
  default:
    name: wecombot-network
