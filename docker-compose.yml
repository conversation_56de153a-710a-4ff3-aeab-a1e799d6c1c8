version: '3.8'

services:
  wecombot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wecombot
    ports:
      - "127.0.0.1:8000:8000"  # 只绑定到localhost，增强安全性
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_BASE_URL=${DEEPSEEK_BASE_URL:-https://api.deepseek.com}
      - WECHAT_CORP_ID=${WECHAT_CORP_ID}
      - WECHAT_CORP_SECRET=${WECHAT_CORP_SECRET}
      - WECHAT_AGENT_ID=${WECHAT_AGENT_ID}
      - WECHAT_TOKEN=${WECHAT_TOKEN}
      - WECHAT_ENCODING_AES_KEY=${WECHAT_ENCODING_AES_KEY}
      - DATABASE_URL=sqlite:///./app.db
      - FAISS_INDEX_PATH=./data/faiss_index
      - DOCUMENTS_PATH=./data/documents
      - APP_HOST=0.0.0.0
      - APP_PORT=8000
      - DEBUG=${DEBUG:-False}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE=./logs/app.log
    volumes:
      - ./data:/app/data:rw
      - ./logs:/app/logs:rw
      - ./app.db:/app/app.db:rw
    restart: unless-stopped
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: false  # 应用需要写入数据和日志
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "--max-time", "10", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # 网络安全
    networks:
      - wecombot-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:1.25-alpine  # 指定具体版本，避免使用latest
    container_name: wecombot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - wecombot
    restart: unless-stopped
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /var/cache/nginx:noexec,nosuid,size=50m
      - /var/run:noexec,nosuid,size=10m
      - /tmp:noexec,nosuid,size=10m
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    networks:
      - wecombot-network
    profiles:
      - production

networks:
  wecombot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
