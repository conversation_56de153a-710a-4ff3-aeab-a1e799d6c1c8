"""
企业微信Webhook处理
"""

import json
import asyncio
from typing import Dict
from fastapi import APIRouter, Request, HTTPException, Query
from fastapi.responses import PlainTextResponse

from app.wechat.client import WeChatClient
from app.services.qa_service import QAService
from app.core.config import settings
from app.core.logger import logger

# 创建企业微信路由器
wechat_router = APIRouter()

# 全局企业微信客户端
wechat_client = WeChatClient()


@wechat_router.get("/callback")
async def wechat_verify(
    msg_signature: str = Query(..., alias="msg_signature"),
    timestamp: str = Query(..., alias="timestamp"),
    nonce: str = Query(..., alias="nonce"),
    echostr: str = Query(..., alias="echostr")
):
    """企业微信回调验证"""
    try:
        # 验证签名
        if settings.WECHAT_TOKEN:
            if wechat_client.verify_signature(
                signature=msg_signature,
                timestamp=timestamp,
                nonce=nonce,
                token=settings.WECHAT_TOKEN
            ):
                logger.info("企业微信回调验证成功")
                return PlainTextResponse(echostr)
            else:
                logger.error("企业微信回调签名验证失败")
                raise HTTPException(status_code=403, detail="签名验证失败")
        else:
            # 如果没有配置token，直接返回echostr（开发环境）
            logger.warning("未配置WECHAT_TOKEN，跳过签名验证")
            return PlainTextResponse(echostr)
            
    except Exception as e:
        logger.error(f"企业微信回调验证失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@wechat_router.post("/callback")
async def wechat_webhook(request: Request):
    """企业微信消息回调处理"""
    try:
        # 获取消息数据
        body = await request.body()
        
        # 如果是加密消息，需要解密（这里简化处理）
        try:
            data = json.loads(body.decode('utf-8'))
        except:
            logger.error("无法解析企业微信消息数据")
            return {"errcode": 0, "errmsg": "ok"}
        
        logger.info(f"收到企业微信消息: {data}")
        
        # 处理不同类型的消息
        msg_type = data.get("MsgType")
        
        if msg_type == "text":
            # 处理文本消息
            await handle_text_message(request, data)
        elif msg_type == "event":
            # 处理事件消息
            await handle_event_message(data)
        else:
            logger.info(f"暂不支持的消息类型: {msg_type}")
        
        return {"errcode": 0, "errmsg": "ok"}
        
    except Exception as e:
        logger.error(f"处理企业微信消息失败: {e}")
        return {"errcode": 0, "errmsg": "ok"}  # 返回成功避免重复推送


async def handle_text_message(request: Request, data: Dict):
    """处理文本消息"""
    try:
        content = data.get("Content", "").strip()
        from_user = data.get("FromUserName", "")
        
        if not content or not from_user:
            return
        
        # 检查是否是@机器人的消息
        question = wechat_client.extract_question_from_message(content)
        
        if not question:
            logger.info(f"消息不包含有效问题: {content}")
            return
        
        logger.info(f"用户 {from_user} 提问: {question}")
        
        # 发送正在处理的提示
        await wechat_client.send_typing_indicator(from_user)
        
        # 异步处理问答
        asyncio.create_task(process_question_async(request, from_user, question))
        
    except Exception as e:
        logger.error(f"处理文本消息失败: {e}")


async def process_question_async(request: Request, user_id: str, question: str):
    """异步处理问答"""
    try:
        # 获取向量存储
        vector_store = request.app.state.vector_store
        qa_service = QAService(vector_store)
        
        # 生成回答
        result = await qa_service.answer_question(
            question=question,
            user_id=user_id
        )
        
        if result["success"]:
            # 格式化回答
            formatted_answer = wechat_client.format_legal_answer(
                answer=result["answer"],
                sources=result["sources"]
            )
            
            # 发送回答
            send_result = await wechat_client.send_text_message(
                user_id=user_id,
                content=formatted_answer
            )
            
            if not send_result["success"]:
                logger.error(f"发送回答失败: {send_result.get('error')}")
        else:
            # 发送错误消息
            error_message = "抱歉，处理您的问题时出现错误，请稍后重试。"
            if "error" in result:
                error_message += f"\n错误信息: {result['error']}"
            
            await wechat_client.send_text_message(
                user_id=user_id,
                content=error_message
            )
        
    except Exception as e:
        logger.error(f"异步处理问答失败: {e}")
        
        # 发送错误提示
        try:
            await wechat_client.send_text_message(
                user_id=user_id,
                content="系统暂时无法处理您的问题，请稍后重试。"
            )
        except:
            pass


async def handle_event_message(data: Dict):
    """处理事件消息"""
    try:
        event_type = data.get("Event")
        from_user = data.get("FromUserName", "")
        
        if event_type == "subscribe":
            # 用户关注事件
            welcome_message = """👋 欢迎使用法规问答助手！

我可以帮助您：
• 解答法律法规相关问题
• 提供合规性分析建议
• 引用相关法规条文

使用方法：
直接发送您的问题即可，例如：
"员工加班时间有什么规定？"

⚠️ 提醒：本助手提供的回答仅供参考，具体情况请咨询专业法律顾问。"""
            
            await wechat_client.send_text_message(
                user_id=from_user,
                content=welcome_message
            )
            
        elif event_type == "unsubscribe":
            # 用户取消关注事件
            logger.info(f"用户 {from_user} 取消关注")
            
        elif event_type == "CLICK":
            # 菜单点击事件
            event_key = data.get("EventKey")
            await handle_menu_click(from_user, event_key)
            
    except Exception as e:
        logger.error(f"处理事件消息失败: {e}")


async def handle_menu_click(user_id: str, event_key: str):
    """处理菜单点击事件"""
    try:
        if event_key == "help":
            help_message = """📖 **使用帮助**

**功能介绍：**
• 法规问答：基于已上传的法规文档回答问题
• 合规检查：分析文件或行为是否符合相关法规
• 条文引用：提供具体的法规条文依据

**使用技巧：**
• 问题尽量具体明确
• 可以询问具体的法规条文
• 支持多轮对话

**示例问题：**
• "劳动合同期限有什么规定？"
• "员工请假制度是怎样的？"
• "加班费如何计算？"

如有其他问题，请直接发送消息咨询。"""
            
            await wechat_client.send_text_message(
                user_id=user_id,
                content=help_message
            )
            
        elif event_key == "stats":
            # 获取统计信息（管理员功能）
            stats_message = "📊 **系统统计**\n\n暂时无法获取统计信息，请联系管理员。"
            
            await wechat_client.send_text_message(
                user_id=user_id,
                content=stats_message
            )
            
    except Exception as e:
        logger.error(f"处理菜单点击失败: {e}")


@wechat_router.post("/send_message")
async def send_message_api(request: Request):
    """发送消息API（用于测试）"""
    try:
        data = await request.json()
        user_id = data.get("user_id")
        content = data.get("content")
        msg_type = data.get("type", "text")
        
        if not user_id or not content:
            raise HTTPException(status_code=400, detail="缺少必要参数")
        
        if msg_type == "text":
            result = await wechat_client.send_text_message(user_id, content)
        elif msg_type == "markdown":
            result = await wechat_client.send_markdown_message(user_id, content)
        else:
            raise HTTPException(status_code=400, detail="不支持的消息类型")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送消息API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@wechat_router.get("/user/{user_id}")
async def get_user_info_api(user_id: str):
    """获取用户信息API"""
    try:
        user_info = await wechat_client.get_user_info(user_id)
        return user_info
        
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
