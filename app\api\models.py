"""
API数据模型定义
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class QuestionRequest(BaseModel):
    """问答请求模型"""
    question: str = Field(..., description="用户问题", min_length=1, max_length=1000)
    user_id: Optional[str] = Field(default="anonymous", description="用户ID")


class QuestionResponse(BaseModel):
    """问答响应模型"""
    answer: str = Field(..., description="回答内容")
    sources: List[Dict[str, Any]] = Field(default=[], description="引用来源")
    response_time: float = Field(..., description="响应时间（秒）")
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(default=None, description="错误信息")


class DocumentUploadResponse(BaseModel):
    """文档上传响应模型"""
    success: bool = Field(..., description="是否成功")
    document_id: Optional[int] = Field(default=None, description="文档ID")
    filename: Optional[str] = Field(default=None, description="文件名")
    message: str = Field(..., description="响应消息")
    error: Optional[str] = Field(default=None, description="错误信息")


class DocumentInfo(BaseModel):
    """文档信息模型"""
    id: int = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    chunk_count: int = Field(..., description="文档块数量")
    status: str = Field(..., description="文档状态")
    upload_time: Optional[str] = Field(default=None, description="上传时间")


class DocumentListResponse(BaseModel):
    """文档列表响应模型"""
    documents: List[DocumentInfo] = Field(..., description="文档列表")
    total: int = Field(..., description="总数量")


class DocumentDetailResponse(BaseModel):
    """文档详情响应模型"""
    id: int = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    file_path: str = Field(..., description="文件路径")
    file_size: int = Field(..., description="文件大小")
    chunk_count: int = Field(..., description="文档块数量")
    status: str = Field(..., description="文档状态")
    upload_time: Optional[str] = Field(default=None, description="上传时间")
    chunks: List[Dict[str, Any]] = Field(default=[], description="文档块信息")


class QAHistoryItem(BaseModel):
    """问答历史项模型"""
    id: int = Field(..., description="记录ID")
    user_id: str = Field(..., description="用户ID")
    question: str = Field(..., description="问题")
    answer: str = Field(..., description="回答")
    created_time: str = Field(..., description="创建时间")
    response_time: Optional[float] = Field(default=None, description="响应时间")


class QAHistoryResponse(BaseModel):
    """问答历史响应模型"""
    history: List[QAHistoryItem] = Field(..., description="历史记录")
    total: int = Field(..., description="总数量")


class PopularQuestion(BaseModel):
    """热门问题模型"""
    question: str = Field(..., description="问题内容")
    count: int = Field(..., description="提问次数")
    latest_time: Optional[str] = Field(default=None, description="最近提问时间")


class StatsResponse(BaseModel):
    """统计信息响应模型"""
    qa_stats: Dict[str, Any] = Field(..., description="问答统计")
    document_stats: Dict[str, Any] = Field(..., description="文档统计")


class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索关键词", min_length=1, max_length=200)
    limit: Optional[int] = Field(default=10, description="返回数量限制", ge=1, le=50)


class SearchResult(BaseModel):
    """搜索结果项模型"""
    document_id: int = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    content_preview: str = Field(..., description="内容预览")
    similarity_score: float = Field(..., description="相似度分数")
    chunk_index: int = Field(..., description="文档块索引")


class SearchResponse(BaseModel):
    """搜索响应模型"""
    results: List[SearchResult] = Field(..., description="搜索结果")
    total: int = Field(..., description="结果总数")
    query: str = Field(..., description="搜索关键词")


class DeleteResponse(BaseModel):
    """删除响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    error: Optional[str] = Field(default=None, description="错误信息")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: str = Field(..., description="检查时间")
    version: str = Field(..., description="版本号")
    components: Dict[str, str] = Field(..., description="组件状态")
