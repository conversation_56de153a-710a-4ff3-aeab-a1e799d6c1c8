# 产品文档：企微群 AI 法规问答机器人

## 一、产品简介

本产品是一款嵌入企业微信群的 AI 智能问答机器人，支持用户通过 @机器人 的方式，结合上传的政策、条例、规章等参考文件，向机器人提问是否符合相关法规。机器人基于大语言模型（如 GPT-4、通义千问等）并集成向量检索（RAG）能力进行回答。

---

## 二、主要功能

### 1. 语义问答

* 群成员可 @机器人 提问，如：“此文件是否符合工会条例？”
* 支持引用本地预设参考文档（如《工会法》《京工办发〔2025〕8号》等）进行多文档合规性分析。

### 2. 文件解析与嵌入

* 支持上传 PDF、Word、TXT 文件进行处理。
* 系统将文件内容切分为段落并嵌入向量数据库，供后续检索。

### 3. 问题匹配与上下文构建

* 用户问题向量化后检索相关段落，结合上下文生成 prompt
* 向大语言模型提交 prompt 并生成符合法规的专业回复。

### 4. 群内智能回复

* 回复格式可为 Markdown，支持引用法规原文、段落编号和分析意见
* 提供文档溯源信息（如来自哪个文件、第几段）

### 5. 后台文档管理

* 可上传预设文件作为知识库
* 支持版本管理、标签管理与文档禁用

---

## 三、技术架构

```text
[企业微信群] → [企业微信自建应用 Webhook 接收] →
→ [AI 问答服务] →
   - 文档向量数据库（FAISS / Qdrant）
   - 文件解析模块（PDF, Word）
   - Embedding 模型（如 bge-small-zh）
   - 大语言模型调用模块（OpenAI / 通义 / Claude）
→ [返回群聊回答]
```

---

## 四、使用流程

1. **群管理员邀请机器人进群**
2. **上传参考文档或后台预设**
3. **成员 @机器人 提问（可附带文件）**
4. **机器人提取问题关键词 → 检索文档片段 → 构造 prompt → 调用大模型**
5. **在群中回复答案（含引用法规）**

---

## 五、使用示例

> 用户上传：工会章程.docx
>
> 用户提问：@机器人 本章程是否符合京工办发〔2025〕8号文件要求？
>
> 🤖 机器人回复：
>
> 根据您提供的文件内容：
>
> > 第三条规定“单位可拒绝职工组建联合组织”，该内容与《京工办发〔2025〕8号》第三条“支持基层建立工会”精神不符。
>
> 建议修订为：“职工有依法成立联合组织的权利”。

---

## 六、注意事项

* 本机器人仅用于政策参考与法律初步解读，不构成法律意见。
* 建议对重要事项仍由法律顾问进行复核。
* 上传文档须为文本可提取格式，建议使用 PDF、DOCX、TXT。

---

## 七、未来规划

* ✅ 支持用户上传私有知识库
* ✅ 增加多轮对话上下文记忆
* ✅ 增加法规更新自动同步功能
* ✅ 增加命令式问答（如“生成修订建议”、“列出冲突条款”）
