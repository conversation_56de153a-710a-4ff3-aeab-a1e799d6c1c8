# 使用Python 3.11官方镜像（更新版本，修复已知漏洞）
FROM python:3.11-slim-bookworm

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 创建非root用户（安全最佳实践）
RUN groupadd --gid 1000 appuser \
    && useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# 更新系统包并安装必要依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    curl \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# 升级pip到最新版本
RUN pip install --upgrade pip setuptools wheel

# 复制依赖文件
COPY --chown=appuser:appuser requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir --user -r requirements.txt

# 创建必要的目录并设置正确的所有权
RUN mkdir -p /app/data/faiss_index \
    && mkdir -p /app/data/documents \
    && mkdir -p /app/logs \
    && chown -R appuser:appuser /app

# 复制应用代码并设置正确的所有权
COPY --chown=appuser:appuser . .

# 设置安全的权限（只给所有者读写权限）
RUN chmod -R 750 /app \
    && chmod -R 755 /app/data \
    && chmod -R 755 /app/logs

# 切换到非root用户
USER appuser

# 将用户的pip安装路径添加到PATH
ENV PATH="/home/<USER>/.local/bin:$PATH"

# 暴露端口
EXPOSE 8000

# 健康检查（使用更安全的方式）
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f --max-time 10 http://localhost:8000/health || exit 1

# 启动命令（以非root用户运行）
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
