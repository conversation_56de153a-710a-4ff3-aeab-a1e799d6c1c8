"""
文档处理服务 - 支持PDF、TXT、Word文件
"""

import os
import re
from typing import List, Tuple
from pathlib import Path
import PyPDF2
from docx import Document as DocxDocument

from app.core.config import settings
from app.core.logger import logger


class DocumentProcessor:
    """文档处理器"""
    
    @staticmethod
    def extract_text_from_file(file_path: str) -> str:
        """从文件中提取文本"""
        try:
            file_extension = Path(file_path).suffix.lower()
            
            if file_extension == '.pdf':
                return DocumentProcessor._extract_from_pdf(file_path)
            elif file_extension == '.txt':
                return DocumentProcessor._extract_from_txt(file_path)
            elif file_extension in ['.docx', '.doc']:
                return DocumentProcessor._extract_from_docx(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_extension}")
                
        except Exception as e:
            logger.error(f"文件文本提取失败 {file_path}: {e}")
            raise
    
    @staticmethod
    def _extract_from_pdf(file_path: str) -> str:
        """从PDF文件提取文本"""
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                    except Exception as e:
                        logger.warning(f"PDF第{page_num + 1}页提取失败: {e}")
                        continue
            
            if not text.strip():
                raise ValueError("PDF文件中未提取到有效文本")
                
            logger.info(f"PDF文件提取完成，共 {len(text)} 个字符")
            return text.strip()
            
        except Exception as e:
            logger.error(f"PDF文件处理失败: {e}")
            raise
    
    @staticmethod
    def _extract_from_txt(file_path: str) -> str:
        """从TXT文件提取文本"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    logger.info(f"TXT文件读取成功，编码: {encoding}，共 {len(text)} 个字符")
                    return text.strip()
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("无法识别文件编码")
            
        except Exception as e:
            logger.error(f"TXT文件处理失败: {e}")
            raise
    
    @staticmethod
    def _extract_from_docx(file_path: str) -> str:
        """从Word文档提取文本"""
        try:
            doc = DocxDocument(file_path)
            text = ""
            
            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"
            
            # 提取表格文本
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text += cell.text + "\n"
            
            if not text.strip():
                raise ValueError("Word文档中未提取到有效文本")
            
            logger.info(f"Word文档提取完成，共 {len(text)} 个字符")
            return text.strip()
            
        except Exception as e:
            logger.error(f"Word文档处理失败: {e}")
            raise
    
    @staticmethod
    def split_text_into_chunks(text: str, chunk_size: int = None, overlap: int = None) -> List[str]:
        """将文本分割成块"""
        chunk_size = chunk_size or settings.MAX_CHUNK_SIZE
        overlap = overlap or settings.CHUNK_OVERLAP
        
        if not text or not text.strip():
            return []
        
        # 清理文本
        text = DocumentProcessor._clean_text(text)
        
        # 如果文本长度小于块大小，直接返回
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            # 确定块的结束位置
            end = start + chunk_size
            
            if end >= len(text):
                # 最后一块
                chunk = text[start:]
            else:
                # 尝试在句号、换行符等位置分割
                chunk = text[start:end]
                
                # 寻找合适的分割点
                split_points = [
                    chunk.rfind('。'),
                    chunk.rfind('！'),
                    chunk.rfind('？'),
                    chunk.rfind('\n'),
                    chunk.rfind('；'),
                    chunk.rfind('，')
                ]
                
                best_split = max([p for p in split_points if p > chunk_size * 0.7])
                
                if best_split > 0:
                    chunk = chunk[:best_split + 1]
                    end = start + best_split + 1
            
            if chunk.strip():
                chunks.append(chunk.strip())
            
            # 计算下一个块的开始位置（考虑重叠）
            start = max(start + 1, end - overlap)
            
            # 避免无限循环
            if start >= len(text):
                break
        
        logger.info(f"文本分块完成，原文本 {len(text)} 字符，分成 {len(chunks)} 块")
        return chunks
    
    @staticmethod
    def _clean_text(text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中文标点）
        text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\w\s.,;:!?()[\]{}""''—…]', '', text)
        
        # 移除多余的换行
        text = re.sub(r'\n\s*\n', '\n', text)
        
        return text.strip()
    
    @staticmethod
    def get_file_info(file_path: str) -> dict:
        """获取文件信息"""
        try:
            stat = os.stat(file_path)
            return {
                "filename": os.path.basename(file_path),
                "file_size": stat.st_size,
                "file_extension": Path(file_path).suffix.lower(),
                "modified_time": stat.st_mtime
            }
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {e}")
            return {}
    
    @staticmethod
    def validate_file(file_path: str) -> bool:
        """验证文件是否有效"""
        try:
            if not os.path.exists(file_path):
                return False
            
            file_extension = Path(file_path).suffix.lower()
            supported_extensions = ['.pdf', '.txt', '.docx', '.doc']
            
            if file_extension not in supported_extensions:
                return False
            
            # 检查文件大小（限制为50MB）
            file_size = os.path.getsize(file_path)
            if file_size > 50 * 1024 * 1024:  # 50MB
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"文件验证失败 {file_path}: {e}")
            return False
