"""
向量存储模块 - 基于FAISS的向量检索
"""

import os
import pickle
import numpy as np
from typing import List, Tuple, Optional
import faiss
from sentence_transformers import SentenceTransformer

from app.core.config import settings
from app.core.logger import logger


class VectorStore:
    """向量存储管理器"""
    
    def __init__(self):
        self.model = None
        self.index = None
        self.texts = []  # 存储原始文本
        self.metadata = []  # 存储元数据
        self.dimension = None
        
        # 文件路径
        self.index_file = f"{settings.FAISS_INDEX_PATH}/faiss.index"
        self.texts_file = f"{settings.FAISS_INDEX_PATH}/texts.pkl"
        self.metadata_file = f"{settings.FAISS_INDEX_PATH}/metadata.pkl"
    
    async def initialize(self):
        """初始化向量存储"""
        try:
            # 加载嵌入模型
            logger.info(f"正在加载嵌入模型: {settings.EMBEDDING_MODEL}")
            self.model = SentenceTransformer(settings.EMBEDDING_MODEL)
            self.dimension = self.model.get_sentence_embedding_dimension()
            logger.info(f"嵌入模型加载完成，维度: {self.dimension}")
            
            # 尝试加载现有索引
            await self._load_index()
            
        except Exception as e:
            logger.error(f"向量存储初始化失败: {e}")
            raise
    
    async def _load_index(self):
        """加载现有的FAISS索引"""
        try:
            if os.path.exists(self.index_file):
                # 加载FAISS索引
                self.index = faiss.read_index(self.index_file)
                logger.info(f"加载FAISS索引成功，包含 {self.index.ntotal} 个向量")
                
                # 加载文本数据
                if os.path.exists(self.texts_file):
                    with open(self.texts_file, 'rb') as f:
                        self.texts = pickle.load(f)
                
                # 加载元数据
                if os.path.exists(self.metadata_file):
                    with open(self.metadata_file, 'rb') as f:
                        self.metadata = pickle.load(f)
                        
                logger.info(f"加载文本数据 {len(self.texts)} 条，元数据 {len(self.metadata)} 条")
            else:
                # 创建新的索引
                self.index = faiss.IndexFlatIP(self.dimension)  # 使用内积相似度
                logger.info("创建新的FAISS索引")
                
        except Exception as e:
            logger.error(f"加载FAISS索引失败: {e}")
            # 创建新索引作为备选
            self.index = faiss.IndexFlatIP(self.dimension)
            self.texts = []
            self.metadata = []
    
    async def add_texts(self, texts: List[str], metadatas: List[dict] = None) -> List[int]:
        """添加文本到向量存储"""
        try:
            if not texts:
                return []
            
            # 生成嵌入向量
            logger.info(f"正在为 {len(texts)} 个文本生成嵌入向量...")
            embeddings = self.model.encode(texts, convert_to_numpy=True)
            
            # 标准化向量（用于内积相似度）
            faiss.normalize_L2(embeddings)
            
            # 添加到索引
            start_idx = len(self.texts)
            self.index.add(embeddings.astype(np.float32))
            
            # 保存文本和元数据
            self.texts.extend(texts)
            if metadatas:
                self.metadata.extend(metadatas)
            else:
                self.metadata.extend([{}] * len(texts))
            
            # 保存索引
            await self._save_index()
            
            indices = list(range(start_idx, start_idx + len(texts)))
            logger.info(f"成功添加 {len(texts)} 个向量到索引，索引范围: {start_idx}-{start_idx + len(texts) - 1}")
            
            return indices
            
        except Exception as e:
            logger.error(f"添加文本到向量存储失败: {e}")
            raise
    
    async def search(self, query: str, k: int = None, threshold: float = None) -> List[Tuple[str, float, dict]]:
        """搜索相似文本"""
        try:
            if not query or self.index.ntotal == 0:
                return []
            
            k = k or settings.MAX_SEARCH_RESULTS
            threshold = threshold or settings.SIMILARITY_THRESHOLD
            
            # 生成查询向量
            query_embedding = self.model.encode([query], convert_to_numpy=True)
            faiss.normalize_L2(query_embedding)
            
            # 搜索
            scores, indices = self.index.search(query_embedding.astype(np.float32), k)
            
            # 过滤结果
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx != -1 and score >= threshold:  # -1表示无效索引
                    text = self.texts[idx] if idx < len(self.texts) else ""
                    metadata = self.metadata[idx] if idx < len(self.metadata) else {}
                    results.append((text, float(score), metadata))
            
            logger.info(f"搜索查询: '{query[:50]}...' 返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []
    
    async def _save_index(self):
        """保存FAISS索引和相关数据"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.index_file), exist_ok=True)
            
            # 保存FAISS索引
            faiss.write_index(self.index, self.index_file)
            
            # 保存文本数据
            with open(self.texts_file, 'wb') as f:
                pickle.dump(self.texts, f)
            
            # 保存元数据
            with open(self.metadata_file, 'wb') as f:
                pickle.dump(self.metadata, f)
                
            logger.debug("FAISS索引和数据保存成功")
            
        except Exception as e:
            logger.error(f"保存FAISS索引失败: {e}")
            raise
    
    async def delete_by_metadata(self, filter_dict: dict):
        """根据元数据删除向量（重建索引）"""
        try:
            # 找到要保留的索引
            keep_indices = []
            for i, metadata in enumerate(self.metadata):
                should_keep = True
                for key, value in filter_dict.items():
                    if metadata.get(key) == value:
                        should_keep = False
                        break
                if should_keep:
                    keep_indices.append(i)
            
            if len(keep_indices) == len(self.texts):
                logger.info("没有找到匹配的向量需要删除")
                return
            
            # 重建索引
            logger.info(f"正在重建索引，保留 {len(keep_indices)}/{len(self.texts)} 个向量")
            
            # 保留的文本和元数据
            new_texts = [self.texts[i] for i in keep_indices]
            new_metadata = [self.metadata[i] for i in keep_indices]
            
            # 重新创建索引
            self.index = faiss.IndexFlatIP(self.dimension)
            self.texts = []
            self.metadata = []
            
            if new_texts:
                await self.add_texts(new_texts, new_metadata)
            else:
                await self._save_index()
            
            logger.info("索引重建完成")
            
        except Exception as e:
            logger.error(f"删除向量失败: {e}")
            raise
    
    def get_stats(self) -> dict:
        """获取向量存储统计信息"""
        return {
            "total_vectors": self.index.ntotal if self.index else 0,
            "dimension": self.dimension,
            "texts_count": len(self.texts),
            "metadata_count": len(self.metadata),
            "model_name": settings.EMBEDDING_MODEL
        }
