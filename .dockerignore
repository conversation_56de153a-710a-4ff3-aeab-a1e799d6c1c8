# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# 虚拟环境
venv/
.venv/
ENV/
env/
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 应用数据（运行时生成）
data/
logs/
*.db
*.sqlite
*.sqlite3

# 文档和说明
README.md
*.md
docs/

# 测试文件
tests/
test_*
*_test.py

# 开发工具
scripts/
.github/

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 备份文件
*.bak
*.backup
backup-*

# 临时文件
tmp/
temp/
*.tmp

# SSL证书
ssl/
*.pem
*.key
*.crt

# 配置文件模板
*.example
