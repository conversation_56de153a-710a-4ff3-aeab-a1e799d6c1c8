#!/bin/bash

# Docker安全扫描脚本

set -e

echo "🔒 开始Docker安全扫描..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查必要工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${YELLOW}⚠️  $1 未安装，跳过相关检查${NC}"
        return 1
    fi
    return 0
}

# 1. 构建镜像
echo "🔨 构建Docker镜像..."
docker build -t wecombot:security-scan .

# 2. 使用Docker Scout扫描（如果可用）
if check_tool "docker"; then
    echo "🔍 使用Docker Scout扫描漏洞..."
    if docker scout version &> /dev/null; then
        docker scout cves wecombot:security-scan --format table
        docker scout recommendations wecombot:security-scan
    else
        echo -e "${YELLOW}⚠️  Docker Scout不可用，跳过扫描${NC}"
    fi
fi

# 3. 使用Trivy扫描（如果可用）
if check_tool "trivy"; then
    echo "🔍 使用Trivy扫描漏洞..."
    trivy image --severity HIGH,CRITICAL wecombot:security-scan
    
    # 扫描配置文件
    echo "🔍 扫描配置文件..."
    trivy config .
else
    echo -e "${YELLOW}⚠️  Trivy未安装，建议安装以进行漏洞扫描${NC}"
    echo "安装命令: curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin"
fi

# 4. 使用Hadolint检查Dockerfile（如果可用）
if check_tool "hadolint"; then
    echo "🔍 使用Hadolint检查Dockerfile..."
    hadolint Dockerfile || echo -e "${YELLOW}⚠️  Dockerfile检查发现问题${NC}"
else
    echo -e "${YELLOW}⚠️  Hadolint未安装，建议安装以检查Dockerfile${NC}"
    echo "安装命令: wget -O hadolint https://github.com/hadolint/hadolint/releases/latest/download/hadolint-Linux-x86_64 && chmod +x hadolint && sudo mv hadolint /usr/local/bin/"
fi

# 5. 检查Python依赖安全性
echo "🔍 检查Python依赖安全性..."
if check_tool "pip"; then
    # 安装safety工具
    pip install safety --quiet || echo -e "${YELLOW}⚠️  无法安装safety工具${NC}"
    
    if check_tool "safety"; then
        safety check -r requirements.txt --json > safety-report.json 2>/dev/null || {
            echo -e "${RED}❌ 发现安全漏洞，请查看safety-report.json${NC}"
        }
        
        # 显示简要报告
        safety check -r requirements.txt --short-report || echo -e "${YELLOW}⚠️  依赖检查完成${NC}"
    fi
fi

# 6. 检查容器配置
echo "🔍 检查Docker Compose配置..."

# 检查是否使用了不安全的配置
check_compose_security() {
    local file="docker-compose.yml"
    local issues=0
    
    echo "检查 $file 安全配置..."
    
    # 检查特权模式
    if grep -q "privileged.*true" "$file"; then
        echo -e "${RED}❌ 发现特权模式配置${NC}"
        issues=$((issues + 1))
    fi
    
    # 检查网络模式
    if grep -q "network_mode.*host" "$file"; then
        echo -e "${RED}❌ 发现host网络模式${NC}"
        issues=$((issues + 1))
    fi
    
    # 检查卷挂载
    if grep -q "/var/run/docker.sock" "$file"; then
        echo -e "${RED}❌ 发现Docker socket挂载${NC}"
        issues=$((issues + 1))
    fi
    
    # 检查端口绑定
    if grep -q '"[0-9]*:[0-9]*"' "$file"; then
        echo -e "${YELLOW}⚠️  端口绑定到所有接口，建议绑定到localhost${NC}"
    fi
    
    if [ $issues -eq 0 ]; then
        echo -e "${GREEN}✅ Docker Compose配置安全检查通过${NC}"
    else
        echo -e "${RED}❌ 发现 $issues 个安全问题${NC}"
    fi
}

check_compose_security

# 7. 运行容器安全基准测试
echo "🔍 运行容器安全检查..."

# 检查容器是否以root用户运行
echo "检查用户权限..."
docker run --rm wecombot:security-scan whoami | grep -q "appuser" && {
    echo -e "${GREEN}✅ 容器以非root用户运行${NC}"
} || {
    echo -e "${RED}❌ 容器以root用户运行${NC}"
}

# 检查文件权限
echo "检查文件权限..."
docker run --rm wecombot:security-scan ls -la /app | head -5

# 8. 生成安全报告
echo "📊 生成安全扫描报告..."
cat > security-report.md << EOF
# Docker安全扫描报告

生成时间: $(date)
镜像: wecombot:security-scan

## 扫描结果摘要

### 基础镜像
- 基础镜像: python:3.11-slim-bookworm
- 用户: 非root用户 (appuser)
- 权限: 最小权限原则

### 安全配置
- ✅ 使用非root用户运行
- ✅ 设置no-new-privileges
- ✅ 配置资源限制
- ✅ 使用自定义网络
- ✅ 端口绑定到localhost

### 建议改进
1. 定期更新基础镜像
2. 监控安全公告
3. 实施运行时安全监控
4. 配置日志审计

## 详细扫描结果

请查看以下文件获取详细信息：
- safety-report.json: Python依赖安全扫描
- trivy-report.json: 容器漏洞扫描（如果可用）

EOF

echo -e "${GREEN}✅ 安全扫描完成！${NC}"
echo "📄 安全报告已生成: security-report.md"

# 清理临时镜像
echo "🧹 清理临时镜像..."
docker rmi wecombot:security-scan || true

echo ""
echo "🔒 安全扫描总结："
echo "1. 请查看 security-report.md 获取详细报告"
echo "2. 如发现高危漏洞，请及时修复"
echo "3. 建议定期运行此脚本进行安全检查"
echo "4. 生产环境部署前请确保所有安全问题已解决"
