"""
问答服务 - 整合向量检索和LLM生成
"""

import time
from typing import Dict, List
from sqlalchemy.orm import Session

from app.core.database import get_db, DatabaseManager
from app.core.vector_store import VectorStore
from app.services.llm_service import LLMService
from app.core.logger import logger


class QAService:
    """问答服务"""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
        self.llm_service = LLMService()
    
    async def answer_question(self, question: str, user_id: str = "anonymous") -> Dict:
        """回答用户问题"""
        start_time = time.time()
        
        try:
            # 1. 向量检索相关文档
            logger.info(f"用户 {user_id} 提问: {question[:100]}...")
            
            search_results = await self.vector_store.search(
                query=question,
                k=5,
                threshold=0.6  # 稍微降低阈值以获取更多相关内容
            )
            
            if not search_results:
                logger.warning(f"未找到相关文档内容，问题: {question[:50]}...")
                return {
                    "answer": "抱歉，我在知识库中没有找到与您问题相关的法规内容。请确认问题是否与已上传的法规文档相关，或联系管理员添加相关文档。",
                    "sources": [],
                    "response_time": time.time() - start_time,
                    "success": False
                }
            
            # 2. 准备上下文数据
            context_chunks = []
            for text, score, metadata in search_results:
                context_chunks.append({
                    "content": text,
                    "similarity_score": score,
                    "filename": metadata.get("filename", "未知文档"),
                    "document_id": metadata.get("document_id"),
                    "chunk_index": metadata.get("chunk_index")
                })
            
            logger.info(f"找到 {len(context_chunks)} 个相关文档片段")
            
            # 3. 调用LLM生成回答
            llm_result = await self.llm_service.generate_answer(
                question=question,
                context_chunks=context_chunks,
                user_id=user_id
            )
            
            response_time = time.time() - start_time
            
            # 4. 记录问答历史
            await self._save_qa_record(
                user_id=user_id,
                question=question,
                answer=llm_result["answer"],
                sources=llm_result["sources"],
                response_time=response_time
            )
            
            result = {
                "answer": llm_result["answer"],
                "sources": llm_result["sources"],
                "response_time": response_time,
                "success": llm_result["success"]
            }
            
            if not llm_result["success"]:
                result["error"] = llm_result.get("error")
            
            logger.info(f"问答完成，用户: {user_id}, 响应时间: {response_time:.2f}s")
            return result
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"问答服务失败: {e}")
            
            return {
                "answer": "抱歉，系统处理您的问题时出现错误，请稍后重试。",
                "sources": [],
                "response_time": response_time,
                "success": False,
                "error": str(e)
            }
    
    async def _save_qa_record(self, user_id: str, question: str, answer: str, 
                            sources: List[Dict], response_time: float):
        """保存问答记录"""
        try:
            # 准备相关文档信息
            related_docs = {
                "sources": sources,
                "source_count": len(sources)
            }
            
            # 保存到数据库
            db = next(get_db())
            try:
                DatabaseManager.create_qa_record(
                    db=db,
                    user_id=user_id,
                    question=question,
                    answer=answer,
                    related_docs=str(related_docs),
                    response_time=response_time
                )
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"保存问答记录失败: {e}")
    
    async def get_qa_history(self, user_id: str = None, limit: int = 10) -> List[Dict]:
        """获取问答历史"""
        try:
            db = next(get_db())
            try:
                from app.core.database import QARecord
                
                query = db.query(QARecord)
                if user_id:
                    query = query.filter(QARecord.user_id == user_id)
                
                records = query.order_by(QARecord.created_time.desc()).limit(limit).all()
                
                history = []
                for record in records:
                    history.append({
                        "id": record.id,
                        "user_id": record.user_id,
                        "question": record.question,
                        "answer": record.answer,
                        "created_time": record.created_time.isoformat(),
                        "response_time": record.response_time
                    })
                
                return history
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取问答历史失败: {e}")
            return []
    
    async def get_popular_questions(self, limit: int = 10) -> List[Dict]:
        """获取热门问题"""
        try:
            db = next(get_db())
            try:
                from app.core.database import QARecord
                from sqlalchemy import func
                
                # 统计相似问题的频次
                popular = db.query(
                    QARecord.question,
                    func.count(QARecord.id).label('count'),
                    func.max(QARecord.created_time).label('latest_time')
                ).group_by(QARecord.question)\
                 .order_by(func.count(QARecord.id).desc())\
                 .limit(limit).all()
                
                result = []
                for question, count, latest_time in popular:
                    result.append({
                        "question": question,
                        "count": count,
                        "latest_time": latest_time.isoformat() if latest_time else None
                    })
                
                return result
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取热门问题失败: {e}")
            return []
    
    def get_stats(self) -> Dict:
        """获取问答服务统计信息"""
        try:
            db = next(get_db())
            try:
                from app.core.database import QARecord
                from sqlalchemy import func
                
                # 统计总问答数
                total_qa = db.query(func.count(QARecord.id)).scalar()
                
                # 统计今日问答数
                from datetime import datetime, timedelta
                today = datetime.now().date()
                today_qa = db.query(func.count(QARecord.id))\
                    .filter(func.date(QARecord.created_time) == today).scalar()
                
                # 统计平均响应时间
                avg_response_time = db.query(func.avg(QARecord.response_time))\
                    .filter(QARecord.response_time.isnot(None)).scalar()
                
                # 获取向量存储统计
                vector_stats = self.vector_store.get_stats()
                
                return {
                    "total_questions": total_qa or 0,
                    "today_questions": today_qa or 0,
                    "avg_response_time": round(float(avg_response_time or 0), 2),
                    "vector_store": vector_stats
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                "total_questions": 0,
                "today_questions": 0,
                "avg_response_time": 0,
                "vector_store": {}
            }
