# 企微群 AI 法规问答机器人

基于RAG技术的企业微信群智能法规问答机器人，支持文档上传、向量检索和智能问答。

## 🚀 功能特性

- **智能问答**: 基于DeepSeek大语言模型的专业法规问答
- **文档处理**: 支持PDF、Word、TXT文件的自动解析和向量化
- **向量检索**: 使用FAISS进行高效的语义相似度搜索
- **企业微信集成**: 无缝接入企业微信群聊
- **RESTful API**: 完整的文档管理和问答API接口
- **Docker部署**: 容器化部署，简单易用

## 📋 系统要求

- Python 3.9+
- Docker & Docker Compose
- 2核4G内存，50G存储空间
- DeepSeek API密钥
- 企业微信自建应用

## 🛠️ 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd wecombot
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑.env文件，填入必要的配置信息
```

关键配置项：
```env
# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 企业微信配置
WECHAT_CORP_ID=your_corp_id_here
WECHAT_CORP_SECRET=your_corp_secret_here
WECHAT_AGENT_ID=your_agent_id_here
```

### 3. 部署服务

```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### 4. 验证部署

访问 http://localhost:8000/health 检查服务状态

## 📖 使用指南

### API接口

服务启动后，可以通过以下接口使用：

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **问答接口**: POST /api/qa/ask
- **文档上传**: POST /api/documents/upload
- **文档管理**: GET /api/documents

### 企业微信配置

1. 在企业微信管理后台创建自建应用
2. 配置应用回调URL: `https://your-domain.com/wechat/callback`
3. 设置应用可见范围
4. 将机器人添加到目标群聊

### 文档上传

支持的文件格式：
- PDF (.pdf)
- Word文档 (.docx, .doc)
- 文本文件 (.txt)

文件大小限制：50MB

### 使用示例

**上传文档**:
```bash
curl -X POST "http://localhost:8000/api/documents/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/document.pdf"
```

**问答查询**:
```bash
curl -X POST "http://localhost:8000/api/qa/ask" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "员工加班时间有什么规定？",
    "user_id": "user123"
  }'
```

## 🧪 开发和测试

### 本地开发

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 运行开发服务器
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 运行测试

```bash
chmod +x scripts/test.sh
./scripts/test.sh
```

### 代码结构

```
wecombot/
├── app/                    # 应用主目录
│   ├── api/               # API路由和模型
│   ├── core/              # 核心配置和工具
│   ├── services/          # 业务逻辑服务
│   └── wechat/            # 企业微信集成
├── tests/                 # 测试文件
├── scripts/               # 部署和工具脚本
├── data/                  # 数据存储目录
├── logs/                  # 日志目录
├── docker-compose.yml     # Docker编排文件
├── Dockerfile            # Docker镜像定义
└── requirements.txt      # Python依赖
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| DEEPSEEK_API_KEY | DeepSeek API密钥 | - | ✅ |
| WECHAT_CORP_ID | 企业微信企业ID | - | ✅ |
| WECHAT_CORP_SECRET | 企业微信应用密钥 | - | ✅ |
| WECHAT_AGENT_ID | 企业微信应用ID | - | ✅ |
| DATABASE_URL | 数据库连接URL | sqlite:///./app.db | ❌ |
| FAISS_INDEX_PATH | FAISS索引路径 | ./data/faiss_index | ❌ |
| DOCUMENTS_PATH | 文档存储路径 | ./data/documents | ❌ |
| MAX_CHUNK_SIZE | 文档分块大小 | 800 | ❌ |
| CHUNK_OVERLAP | 分块重叠大小 | 100 | ❌ |

### 模型配置

默认使用的模型：
- **嵌入模型**: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
- **语言模型**: DeepSeek Chat API

## 📊 监控和维护

### 日志查看

```bash
# 查看应用日志
docker-compose logs -f wecombot

# 查看特定时间的日志
docker-compose logs --since="2024-01-01T00:00:00" wecombot
```

### 数据备份

```bash
# 备份数据目录
tar -czf backup-$(date +%Y%m%d).tar.gz data/

# 备份数据库
cp app.db backup-$(date +%Y%m%d).db
```

### 性能监控

- 内存使用: `docker stats wecombot`
- 磁盘使用: `du -sh data/`
- API响应时间: 查看 `/api/stats` 接口

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   - 检查环境变量配置
   - 查看Docker日志: `docker-compose logs wecombot`
   - 确认端口8000未被占用

2. **API调用失败**
   - 验证DeepSeek API密钥是否有效
   - 检查网络连接
   - 查看应用日志

3. **企业微信回调失败**
   - 确认回调URL配置正确
   - 检查服务器防火墙设置
   - 验证企业微信应用配置

4. **文档处理失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过50MB
   - 查看文档处理日志

### 日志级别

可通过环境变量 `LOG_LEVEL` 调整日志级别：
- DEBUG: 详细调试信息
- INFO: 一般信息（默认）
- WARNING: 警告信息
- ERROR: 错误信息

## 🔒 安全注意事项

1. **API密钥安全**: 妥善保管DeepSeek API密钥，不要提交到代码仓库
2. **网络安全**: 生产环境建议使用HTTPS
3. **访问控制**: 配置适当的防火墙规则
4. **数据备份**: 定期备份重要数据
5. **日志安全**: 避免在日志中记录敏感信息

## 📈 性能优化

1. **向量索引优化**: 定期重建FAISS索引
2. **缓存策略**: 考虑添加Redis缓存热门问答
3. **负载均衡**: 生产环境可部署多个实例
4. **数据库优化**: 大量数据时考虑使用PostgreSQL

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

⚠️ **免责声明**: 本机器人提供的法规解答仅供参考，不构成法律意见。重要事项请咨询专业法律顾问。
