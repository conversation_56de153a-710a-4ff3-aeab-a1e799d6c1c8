"""
数据库配置和模型定义
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Generator

from app.core.config import settings
from app.core.logger import logger

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()


class Document(Base):
    """文档表模型"""
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    upload_time = Column(DateTime, default=func.now())
    file_size = Column(Integer)
    chunk_count = Column(Integer, default=0)
    status = Column(String(50), default="active")  # active, inactive, processing, error
    
    # 关联文档块
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")


class DocumentChunk(Base):
    """文档块表模型"""
    __tablename__ = "document_chunks"
    
    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    content = Column(Text, nullable=False)
    embedding_index = Column(Integer)  # FAISS索引中的位置
    
    # 关联文档
    document = relationship("Document", back_populates="chunks")


class QARecord(Base):
    """问答记录表模型"""
    __tablename__ = "qa_records"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), nullable=False)
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=False)
    related_docs = Column(Text)  # JSON格式存储相关文档信息
    created_time = Column(DateTime, default=func.now())
    response_time = Column(Float)  # 响应时间（秒）


async def init_db():
    """初始化数据库"""
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def get_db() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# 数据库操作辅助函数
class DatabaseManager:
    """数据库管理器"""
    
    @staticmethod
    def create_document(db: Session, filename: str, file_path: str, file_size: int) -> Document:
        """创建文档记录"""
        document = Document(
            filename=filename,
            file_path=file_path,
            file_size=file_size,
            status="processing"
        )
        db.add(document)
        db.commit()
        db.refresh(document)
        return document
    
    @staticmethod
    def create_document_chunk(db: Session, document_id: int, chunk_index: int, 
                            content: str, embedding_index: int) -> DocumentChunk:
        """创建文档块记录"""
        chunk = DocumentChunk(
            document_id=document_id,
            chunk_index=chunk_index,
            content=content,
            embedding_index=embedding_index
        )
        db.add(chunk)
        db.commit()
        db.refresh(chunk)
        return chunk
    
    @staticmethod
    def update_document_status(db: Session, document_id: int, status: str, chunk_count: int = None):
        """更新文档状态"""
        document = db.query(Document).filter(Document.id == document_id).first()
        if document:
            document.status = status
            if chunk_count is not None:
                document.chunk_count = chunk_count
            db.commit()
    
    @staticmethod
    def create_qa_record(db: Session, user_id: str, question: str, answer: str, 
                        related_docs: str = None, response_time: float = None) -> QARecord:
        """创建问答记录"""
        qa_record = QARecord(
            user_id=user_id,
            question=question,
            answer=answer,
            related_docs=related_docs,
            response_time=response_time
        )
        db.add(qa_record)
        db.commit()
        db.refresh(qa_record)
        return qa_record
    
    @staticmethod
    def get_documents(db: Session, skip: int = 0, limit: int = 100):
        """获取文档列表"""
        return db.query(Document).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_document_by_id(db: Session, document_id: int):
        """根据ID获取文档"""
        return db.query(Document).filter(Document.id == document_id).first()
    
    @staticmethod
    def delete_document(db: Session, document_id: int):
        """删除文档"""
        document = db.query(Document).filter(Document.id == document_id).first()
        if document:
            db.delete(document)
            db.commit()
            return True
        return False
