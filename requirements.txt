# Web框架 - 使用最新稳定版本修复安全漏洞
fastapi==0.108.0
uvicorn[standard]==0.25.0
pydantic==2.5.3
pydantic-settings==2.1.0

# 企业微信相关 - 更新到安全版本
requests==2.31.0
cryptography==42.0.0

# 文档处理 - 更新版本修复已知漏洞
PyPDF2==3.0.1
python-docx==1.1.0
python-multipart==0.0.6

# 向量检索和AI - 使用稳定版本
sentence-transformers==2.2.2
faiss-cpu==1.7.4
numpy==1.26.2

# DeepSeek API - 更新到最新版本
openai==1.6.1

# 数据库 - 使用最新稳定版本
sqlalchemy==2.0.25

# 工具库 - 更新版本
python-dotenv==1.0.0
loguru==0.7.2
aiofiles==23.2.0

# 安全工具
safety==2.3.5

# 开发和测试
pytest==7.4.4
pytest-asyncio==0.23.2
black==23.12.1
httpx==0.26.0  # 用于测试HTTP请求
