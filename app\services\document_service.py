"""
文档管理服务 - 处理文档上传、解析、向量化
"""

import os
import shutil
import asyncio
from typing import List, Dict, Optional
from pathlib import Path
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db, DatabaseManager
from app.core.vector_store import VectorStore
from app.services.document_processor import DocumentProcessor
from app.services.llm_service import LLMService
from app.core.logger import logger


class DocumentService:
    """文档管理服务"""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
        self.document_processor = DocumentProcessor()
        self.llm_service = LLMService()
    
    async def upload_document(self, file_path: str, filename: str = None) -> Dict:
        """上传并处理文档"""
        try:
            # 验证文件
            if not self.document_processor.validate_file(file_path):
                return {
                    "success": False,
                    "error": "文件格式不支持或文件过大（限制50MB）"
                }
            
            # 获取文件信息
            file_info = self.document_processor.get_file_info(file_path)
            filename = filename or file_info["filename"]
            
            logger.info(f"开始处理文档: {filename}")
            
            # 创建数据库记录
            db = next(get_db())
            try:
                # 移动文件到文档目录
                doc_dir = Path(settings.DOCUMENTS_PATH)
                doc_dir.mkdir(parents=True, exist_ok=True)
                
                # 生成唯一文件名
                file_extension = Path(filename).suffix
                base_name = Path(filename).stem
                counter = 1
                new_filename = filename
                
                while (doc_dir / new_filename).exists():
                    new_filename = f"{base_name}_{counter}{file_extension}"
                    counter += 1
                
                new_file_path = doc_dir / new_filename
                shutil.copy2(file_path, new_file_path)
                
                # 创建文档记录
                document = DatabaseManager.create_document(
                    db=db,
                    filename=new_filename,
                    file_path=str(new_file_path),
                    file_size=file_info["file_size"]
                )
                
                # 异步处理文档内容
                asyncio.create_task(self._process_document_content(document.id, str(new_file_path)))
                
                return {
                    "success": True,
                    "document_id": document.id,
                    "filename": new_filename,
                    "message": "文档上传成功，正在后台处理中..."
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"文档上传失败: {e}")
            return {
                "success": False,
                "error": f"文档上传失败: {str(e)}"
            }
    
    async def _process_document_content(self, document_id: int, file_path: str):
        """处理文档内容（异步）"""
        db = next(get_db())
        try:
            logger.info(f"开始处理文档内容，文档ID: {document_id}")
            
            # 1. 提取文本
            text_content = self.document_processor.extract_text_from_file(file_path)
            
            if not text_content or len(text_content.strip()) < 10:
                DatabaseManager.update_document_status(db, document_id, "error")
                logger.error(f"文档 {document_id} 提取的文本内容过少")
                return
            
            # 2. 分割文本
            chunks = self.document_processor.split_text_into_chunks(text_content)
            
            if not chunks:
                DatabaseManager.update_document_status(db, document_id, "error")
                logger.error(f"文档 {document_id} 分块失败")
                return
            
            # 3. 准备元数据
            filename = os.path.basename(file_path)
            metadatas = []
            for i, chunk in enumerate(chunks):
                metadatas.append({
                    "document_id": document_id,
                    "filename": filename,
                    "chunk_index": i,
                    "chunk_length": len(chunk)
                })
            
            # 4. 向量化并存储
            embedding_indices = await self.vector_store.add_texts(chunks, metadatas)
            
            # 5. 保存文档块记录
            for i, (chunk, embedding_index) in enumerate(zip(chunks, embedding_indices)):
                DatabaseManager.create_document_chunk(
                    db=db,
                    document_id=document_id,
                    chunk_index=i,
                    content=chunk,
                    embedding_index=embedding_index
                )
            
            # 6. 更新文档状态
            DatabaseManager.update_document_status(db, document_id, "active", len(chunks))
            
            logger.info(f"文档 {document_id} 处理完成，共 {len(chunks)} 个块")
            
        except Exception as e:
            logger.error(f"文档内容处理失败，文档ID {document_id}: {e}")
            DatabaseManager.update_document_status(db, document_id, "error")
        finally:
            db.close()
    
    async def get_documents(self, skip: int = 0, limit: int = 100) -> List[Dict]:
        """获取文档列表"""
        try:
            db = next(get_db())
            try:
                documents = DatabaseManager.get_documents(db, skip, limit)
                
                result = []
                for doc in documents:
                    result.append({
                        "id": doc.id,
                        "filename": doc.filename,
                        "file_size": doc.file_size,
                        "chunk_count": doc.chunk_count,
                        "status": doc.status,
                        "upload_time": doc.upload_time.isoformat() if doc.upload_time else None
                    })
                
                return result
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            return []
    
    async def get_document_detail(self, document_id: int) -> Optional[Dict]:
        """获取文档详情"""
        try:
            db = next(get_db())
            try:
                document = DatabaseManager.get_document_by_id(db, document_id)
                
                if not document:
                    return None
                
                # 获取文档块信息
                chunks_info = []
                for chunk in document.chunks:
                    chunks_info.append({
                        "chunk_index": chunk.chunk_index,
                        "content_preview": chunk.content[:200] + "..." if len(chunk.content) > 200 else chunk.content,
                        "content_length": len(chunk.content)
                    })
                
                return {
                    "id": document.id,
                    "filename": document.filename,
                    "file_path": document.file_path,
                    "file_size": document.file_size,
                    "chunk_count": document.chunk_count,
                    "status": document.status,
                    "upload_time": document.upload_time.isoformat() if document.upload_time else None,
                    "chunks": chunks_info
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取文档详情失败: {e}")
            return None
    
    async def delete_document(self, document_id: int) -> Dict:
        """删除文档"""
        try:
            db = next(get_db())
            try:
                # 获取文档信息
                document = DatabaseManager.get_document_by_id(db, document_id)
                if not document:
                    return {
                        "success": False,
                        "error": "文档不存在"
                    }
                
                # 从向量存储中删除
                await self.vector_store.delete_by_metadata({"document_id": document_id})
                
                # 删除物理文件
                try:
                    if os.path.exists(document.file_path):
                        os.remove(document.file_path)
                except Exception as e:
                    logger.warning(f"删除物理文件失败: {e}")
                
                # 从数据库删除
                success = DatabaseManager.delete_document(db, document_id)
                
                if success:
                    logger.info(f"文档 {document_id} 删除成功")
                    return {
                        "success": True,
                        "message": "文档删除成功"
                    }
                else:
                    return {
                        "success": False,
                        "error": "数据库删除失败"
                    }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return {
                "success": False,
                "error": f"删除文档失败: {str(e)}"
            }
    
    async def search_documents(self, query: str, limit: int = 10) -> List[Dict]:
        """搜索文档内容"""
        try:
            # 使用向量搜索
            search_results = await self.vector_store.search(query, k=limit)
            
            # 整理结果
            results = []
            seen_docs = set()
            
            for text, score, metadata in search_results:
                doc_id = metadata.get("document_id")
                if doc_id not in seen_docs:
                    seen_docs.add(doc_id)
                    results.append({
                        "document_id": doc_id,
                        "filename": metadata.get("filename", "未知文档"),
                        "content_preview": text[:200] + "..." if len(text) > 200 else text,
                        "similarity_score": score,
                        "chunk_index": metadata.get("chunk_index", 0)
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"搜索文档失败: {e}")
            return []
    
    def get_stats(self) -> Dict:
        """获取文档统计信息"""
        try:
            db = next(get_db())
            try:
                from app.core.database import Document
                from sqlalchemy import func
                
                # 统计文档数量
                total_docs = db.query(func.count(Document.id)).scalar()
                active_docs = db.query(func.count(Document.id))\
                    .filter(Document.status == "active").scalar()
                processing_docs = db.query(func.count(Document.id))\
                    .filter(Document.status == "processing").scalar()
                error_docs = db.query(func.count(Document.id))\
                    .filter(Document.status == "error").scalar()
                
                # 统计文件大小
                total_size = db.query(func.sum(Document.file_size)).scalar() or 0
                
                # 统计文档块数量
                total_chunks = db.query(func.sum(Document.chunk_count)).scalar() or 0
                
                return {
                    "total_documents": total_docs or 0,
                    "active_documents": active_docs or 0,
                    "processing_documents": processing_docs or 0,
                    "error_documents": error_docs or 0,
                    "total_file_size": total_size,
                    "total_chunks": total_chunks
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取文档统计失败: {e}")
            return {
                "total_documents": 0,
                "active_documents": 0,
                "processing_documents": 0,
                "error_documents": 0,
                "total_file_size": 0,
                "total_chunks": 0
            }
