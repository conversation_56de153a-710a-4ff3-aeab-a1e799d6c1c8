"""
企微群 AI 法规问答机器人 - 主应用入口
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
import os
from dotenv import load_dotenv

from app.core.config import settings
from app.core.database import init_db
from app.core.vector_store import VectorStore
from app.api.routes import api_router
from app.wechat.webhook import wechat_router
from app.core.logger import logger

# 加载环境变量
load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("正在启动企微群 AI 法规问答机器人...")
    
    # 初始化数据库
    await init_db()
    logger.info("数据库初始化完成")
    
    # 初始化向量存储
    vector_store = VectorStore()
    await vector_store.initialize()
    app.state.vector_store = vector_store
    logger.info("向量存储初始化完成")
    
    logger.info("应用启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("正在关闭应用...")

# 创建FastAPI应用
app = FastAPI(
    title="企微群 AI 法规问答机器人",
    description="基于RAG技术的企业微信群智能法规问答机器人",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(api_router, prefix="/api")
app.include_router(wechat_router, prefix="/wechat")

@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "企微群 AI 法规问答机器人",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.APP_HOST,
        port=settings.APP_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
